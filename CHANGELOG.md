# [prd:v3.0.0] 19 June 2025

1. Upgraded laravel framework to v11
2. Update student grading framework job to just load id instead of whole config
3. [^1]Added `admission_grade_id` to enrollment session crud api
4. [SKLEARN-474](https://skribblelab-team.atlassian.net/browse/SKLEARN-474) Added translations to guest info
5. [^1]Examination result by exam report fixed column when generate for multiple grades
6. [^1]Enrollment user enhancements
7. [^1]Examination report display decimal issue fixed
8. Enrollment admin - make guardian phone number optional if guardian passed away

[^1]: Changes affecting Frontend

---

# [prd:v2.5.6] 18 June 2025

1. Added new font to display traditional chinese words

---

# [prd:v2.5.5] 16 June 2025

1. Added Net Average Report
2. Added subject analysis report
3. Added subject average mark report
4. Added subject Passing rate report
5. Fix assign class bugs (delete problem, saving existing student will add back the class subject issue)
6. Added `sibling_student_numbers` for enrollment admin/non admin update
7. nric/passport is required for parent and cannot be duplicated
8. Added `siblings` key for enrollment show api
9. Added `pending` status in enrollment session summary page

---

# [prd:v2.5.4] 16 June 2025

1. Enhanced examination result by class report to check is_exempted (is_exempted now shows empty)

---

# [prd:v2.5.3] 16 June 2025

1. Examination result by student added principal and homeroom teacher name
2. [Issue 180] Update Semester Class (Primary) - By Student in Class report title and remove leave school legend
3. [Issue 191] Sort non primary report by semester class with primary class ascending
4. Return`student_id`and`result_source_subject_id`on mark entry even if error

---

# [prd:v2.5.2] 13 June 2025

1. Added enrollment summary in enrollment session show
2. Added `with_position_in_standard` for report `Examination Result/Average Report (By Class)` → by default will always be false if not set (FE default this checkbox to true)

@yinwen can help to work on 2

---

# [prd:v2.5.1] 12 June 2025

1. Added Marks Added label change
2. Added Student Report Card (By Group)
3. Added Examination Result by Class
4. Added Examination Result by Exam
5. Added Examination Result by Student
6. Updated enrollment email format

---

# [prd:v2.5.0] 11 June 2025

1. Exam module changes

---

# [prd:v2.4.0] 9 June 2025

1. Go Live Enrollment Module
2. Added Duplicate semester setting feature

---

# [prd:v2.3.2] 6 June 2025

1. [Issue 140/141] Added non primary class report
2. [Issue 173] Fee payment bypass month fix

---

# [prd:v2.3.1] 3 June 2025

1. Billing document by daily collection export timeout fix

---

# [prd:v2.3.0] 28 May 2025

1. Fixed student absent report issue

---

# [prd:v2.2.7] 27 May 2025

1. Exam module deploy
2. Fix wallet transaction reference number clash issue

---

# [prd:v2.2.6] 21 May 2025

1. Remove cronjob to inactivate discount

---

# [prd:v2.2.5] 21 May 2025

1. Added `NOT_ARRIVED` flag to hostel in out record repository
2. [Issue 126] Filter report by leave application type
3. Added guardian resource into HostelInOutRecordRepository

---

# [prd:v2.2.4] 15 May 2025

1. Repository fixes
2. Added `has_mark_deduction` flag for timeslots/period_attendances table, this will
    1. Determine if the period requires mark deduction (Like second 班务）
    2. If period doesn’t requires mark deduction, leave application history will not display ✅
3. When updating timetable timeslots, need to pass in 2 new param
    1. `timeslots[]['default_init_status']` → Required, Accepts value `PRESENT, ABSENT, LATE`
    2. `timeslots[]['has_mark_defuction']` → Required, accepts boolean
4. Patched attendance data
    1. Update period attendance to present if school attendance is present/late (2025-04-21 - 2025-04-23)
    2. Update all period_attendances.has_mark_deduction to be equal with timeslots.has_mark_deduction
    3. Remove all timeslot_teacher for Thursday/Friday second 班务
    4. start from 2025-04-21, if school attendance PRESENT but check out status = null (forgot tap card), create (早退/无签离）leave application (if period 15 don’t have leave application)

---

# [prd:v2.2.3] 8 May 2025

1. Class attendance taking fix for showing wrong substitute periods during attendance taking
2. [Issue 83] Newly created reward punishment record auto set to POSTED
3. [Issue 159] Canteen product page too long to load
4. [Issue 149] Add max wallet topup value
5. [Issue 96] Added `call_no_wildcard` for book search
6. [Issue 156] Co-curricular statistic report add Code Column
7. [Issue 24] Teacher attendance taking report

---

# [prd:v2.2.2] 7 May 2025

1. Attendance posting use update instead of delete + create to prevent primary key issue
2. [Issue 103,114] Return `updated_by_employee` and `photo` to class attendance taking
3. [Issue 135] Prevent attendance card tapping if user card has been inactivated
4. [Issue 136] Substitute management index change to show period label instead
5. [Issue 85] Update formatting, update text translation
6. [Issue 88] Fix library module loan calculation
7. Ran checkout posting → No issue

TODO:

```sql
php
artisan migrate
php artisan posting:student-attendance-input --process-check-out

Update 早休，午休，休息 to can_apply_leave = false
```

---

# [prd:v2.2.1] 4 May 2025

1. [Issue 134] Rearrange column in Daily Collection Report
2. [Issue 116] Student Class search displaying old class
3. [Issue 86] Permission dependency fix
4. [Issue 104] Discount page pagination fix

@Wilson Issue 24, 50, 85, 135, 136 Need to be tested ASAP, 59 & 60 isit tested?

---

# [prd:v2.2.0] 29 Apr 2025

1. [Issue 106] Remove compulsory for signing guardian
2. [Issue 105] Updated bank charges formula
3. [Issue 96] Added `book_no_wildcard` for book index API
4. [Issue 90/91] Library page show student class
5. [Issue 36] Fix description disappear when discount is confirmed
6. [Issue 104] Sort by id desc by default so wont have sorting issues

---

# [prd:v2.1.0] 28 Apr 2025

1. [Issue 80] Return scholarship details in Discount index/show API
2. [Issue 40] Validate don’t allow void billing doc if got active payment gateway log
3. [Issue 71] Parent to pay all fees in the same month, admin doesn’t have this limitation

---

# [prd:v2.0.6] 25 Apr 2025

1. Student Absent Report header fix
2. Eager Loading for reward punishment records,
    1. Added `include` studentLatestPrimaryClasses when calling api to get `student_class` to render on FE student view page tab
3. Fixed Daily collection report filter by product issue
4. Fixed class subject not showing pagination issue
5. Patched hostel guardians keyed in v1 after 2025-01-01

---

# [prd:v2.0.5] 24 Apr 2025

1. School attendance now post whenever student tap card

   → Applies to tap card online api and bulk submit api

2. Added Student Absent Report
3. Billing document index add `payment_reference_no` filter
4. Added Mark deduction report `POSTMAN: Reports → Attendance → Report by Student Attendance Mark Deduction`

---

# [prd:v2.0.4] 22 Apr 2025

1. Issue on calculating invoice with multiple discount
2. Patched 早退/无签离 and 放学后无拍卡免扣 to second 班务
3. Updated init to generate second 班务 as PRESENT
4. Updated student attendance posting to auto create 早退/无签离 leave application if student forgot to tap card when leave school
5. [Issue 40] Prevent to void paid invoice
6. [Issue 16] Add total into daily collection report
7. [Issue 42] Contractor Daily Attendance Report

FE Things to deployed

1. [Issue 15] Add type `DEPOSIT` and `TRANSFER` on wallet transaction report
2. [Issue 20] Student search engine change to `student_number_wildcard` for search
3. [Issue 29] When student borrow book, due date should populate from book info, not hardcoded to 7 days later
4. [Issue 30] After borrow book, reset form

---

# [prd:v2.0.3] 21 Apr 2025

1. If student attendance = absent, is_editable = false
2. Eager load `/semester-classes` api
3. Updated period label name

---

# [v2.0.1] 21 Apr 2025

1. Added Reports
    1. Student Analysis Report
    2. Student Attendance Report
    3. Class Attendance Report
    4. Attendance Report By Attendance Summary
2. Fixed Migration

---

# [v1.0.12] 04 Mar 2025

1. Remove logic for student to transfer money to primary guardian once they left school
2. Added wallet refund function
3. Added wallet withdraw function
4. Added wallet adjustment function
5. Add adhoc notification to be sent out when ecommerce order completes
6. Added terminal name in POS report

---

# [v1.0.11] 27 Feb 2025

1. Enhance announcement image to store in s3 and chunk records to be processed

   Lucas deployed and tested on dev ✅

---

# [v1.0.10] 24 Feb 2025

1. Fix weekly canteen report to show next week data
2. Added `is_direct_dependant` update for student CRUD
3. Permission dependency update (No longer have to workaround to assign dependency permission one by one)
4. Added guardian name/phone number/email search for student index page
5. Searching guardian phone/name/email on admin wallet page will now return student information
6. Invoice dynamically set `userable` No. instead of hardcoded to student number

## FE Changes

- added`guardian_name`,`guardian_phone_number`, and`guardian_email`to student filter
- added`is_direct_dependant`and default checked it and`with_user_account`
- fixed file upload error display
- add image minimumCacheTTL and set icon images to unoptimized

---

# [v1.0.9] 17 Feb 2025

1. Update Rate Limiter to below:
    1. API routes used by BO and APP → 60 per minute
    2. API routes used by 3rd party (payex, oprator app, etc) → 500 per minute
2. Payex set payment intent to expire in 5mins
3. Payex cronjob to process data between (last 2hours - last hour) only
4. Sort canteen report by class code

---

# [v1.0.8] 14 Feb 2025

1. Added `email` and `phone_number` for user update
2. Fix Announcements to only send message to direct dependants
3. `/employees` route now uses Simple Resource
4. Fix guardian - when updating guardian phone number, it should not create a new user
5. Separate Payex router and callback url
6. Create credit note for cancelled invoice
7. Patched invoice number running number

---

# [v1.0.7] 14 Feb 2025

1. Optimize queries with eager loading
2. Change card import to deactivate old card and add new card for students/employee/contractor that already have an active card
3. Product assign delivery date and assign available date group by product groups
4. Add merchant restriction to all reports

---

# [v1.0.6] 11 Feb 2025

1. Remove part where BO API will call Payex side again to get latest transaction data, just process using received payload
2. Added Auditable for models that are being used in phase 1
3. Added outbound logging for Payex record
4. Fixed Employee status disappearing issue

---

# [v1.0.5] 9 Feb 2025

1. Add `is_direct_dependant` to `guardian_student` table

---

# [v1.0.4] 5 Feb 2025

1. Added inbound log for payment gateway callback
2. Update APIs to use SimpleEmployeeResource:
    1. Conduct setting
    2. Counselling Case Record
    3. Hostel Reward Punishment
    4. Semester Class
3. Add `getTransactionByReferenceNumber` for cronjob payex status update
4. Added trait `HandlesPagination` as a helper to determine return all/paginated
5. Increased invoice label width to 200px
6. Set guardian email to nullable
7. Added `delivery_date_from`, `delivery_date_to` and `per_page = -1` filter for `products` route
8. Set payex callback time to UTC
9. Update semester class index to use eager loading

---

# [v1.0.3] 4 Feb 2025

- `conduct-settings/get-teachers-by-semester-class/{semester_class}` now only return primary teachers
- Contractor now cannot be deleted if he/she is linked to any card/class subject
- Optimize product tag and announcement crud page
- Added `leave_status` to student API
- When student left school, set all active student class to active = false
- Set employee `job_title_id, address, address2` to compulsory
- Added `category_id, sub_category_id, tag_id, group_id` filter on routes below:
    - `products/merchant-type/{merchant_type}`
    - `products/by-userable`
    - `products`
- Fixed migration data for employee session (This fixes employee session cannot be deleted once first created issue)
- Added `selected_semester_class` on student index API

---

# [v1.0.2] 31 Jan 2025

- Ignore ErrorCodeHelper codes from pushing to sentry

---

# [v1.0.1] 27 Jan 2025

- Added userable in wallet_transactions table
- Payment gateway cancel fix
- Userable access fix for routes:
    - Ecommerce checkout
    - Ecommerce view bookshop/canteen
    - Wallet deposit
    - Wallet transfer
- Update user inbox message to ISO format
