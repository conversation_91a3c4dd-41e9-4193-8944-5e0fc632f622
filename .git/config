[core]
	repositoryformatversion = 0
	filemode = true
	bare = false
	logallrefupdates = true
	ignorecase = true
	precomposeunicode = true
[user]
	email = <EMAIL>
	name = Sim <PERSON>hen <PERSON>uan
[remote "origin"]
	url = *****************:skribblelab/skribble-learn-api.git
	fetch = +refs/heads/*:refs/remotes/origin/*
[branch "main"]
	remote = origin
	merge = refs/heads/main
[branch "add-internationalization-api"]
	remote = origin
	merge = refs/heads/add-internationalization-api
[branch "add-test-cases-for-login"]
	remote = origin
	merge = refs/heads/add-test-cases-for-login
[branch "master-race"]
	remote = origin
	merge = refs/heads/master-race
[branch "master-religion"]
	remote = origin
	merge = refs/heads/master-religion
[branch "master-country-and-state"]
	remote = origin
	merge = refs/heads/master-country-and-state
[branch "master-school-profile"]
	remote = origin
	merge = refs/heads/master-school-profile
[branch "add-media"]
	remote = origin
	merge = refs/heads/add-media
[branch "master-config"]
	remote = origin
	merge = refs/heads/master-config
[branch "master-semester-settings"]
	remote = origin
	merge = refs/heads/master-semester-settings
[branch "add-permissions"]
	remote = origin
	merge = refs/heads/add-permissions
[branch "add-grades"]
	remote = origin
	merge = refs/heads/add-grades
[branch "add-student"]
	remote = origin
	merge = refs/heads/add-student
[branch "ewallet"]
	remote = origin
	merge = refs/heads/ewallet
[branch "transaction-module-backup"]
	remote = origin
	merge = refs/heads/transaction-module-backup
[branch "qas"]
	remote = origin
	merge = refs/heads/qas
[branch "feature/class"]
	remote = origin
	merge = refs/heads/feature/class
[branch "ewallet-enhancements"]
	remote = origin
	merge = refs/heads/ewallet-enhancements
[branch "feature/employee"]
	remote = origin
	merge = refs/heads/feature/employee
[branch "add-attendance"]
	remote = origin
	merge = refs/heads/add-attendance
[branch "review-wallet-features"]
	remote = origin
	merge = refs/heads/review-wallet-features
[branch "config-enhancements"]
	remote = origin
	merge = refs/heads/config-enhancements
[branch "feature/class-assignment-to-students"]
	remote = origin
	merge = refs/heads/feature/class-assignment-to-students
[branch "refactor-test-case"]
	remote = origin
	merge = refs/heads/refactor-test-case
[branch "feature/hostel-block-CRUD"]
	remote = origin
	merge = refs/heads/feature/hostel-block-CRUD
[branch "feature/hostel-room-CRUD"]
	remote = origin
	merge = refs/heads/feature/hostel-room-CRUD
[branch "feature/enrolment"]
	remote = origin
	merge = refs/heads/feature/enrolment
[branch "feature/hostel-room-beds-CRUD"]
	remote = origin
	merge = refs/heads/feature/hostel-room-beds-CRUD
[branch "feature/hostel-merit-demerit"]
	remote = origin
	merge = refs/heads/feature/hostel-merit-demerit
[branch "feature/bed-assignments"]
	remote = origin
	merge = refs/heads/feature/bed-assignments
[branch "library"]
	remote = origin
	merge = refs/heads/library
[branch "library-book"]
	remote = origin
	merge = refs/heads/library-book
[branch "feature/enrollment-fix"]
	remote = origin
	merge = refs/heads/feature/enrollment-fix
[branch "add-courses-crud"]
	remote = origin
	merge = refs/heads/add-courses-crud
[branch "email-login"]
	remote = origin
	merge = refs/heads/email-login
[branch "feature/check-in-out-API"]
	remote = origin
	merge = refs/heads/feature/check-in-out-API
[branch "feature/user-special-setting-CRUD"]
	remote = origin
	merge = refs/heads/feature/user-special-setting-CRUD
[branch "feature/hostel-reward-punishment-settings"]
	remote = origin
	merge = refs/heads/feature/hostel-reward-punishment-settings
[branch "feature/hostel-reward-punishment-records"]
	remote = origin
	merge = refs/heads/feature/hostel-reward-punishment-records
[branch "hostel-enhancement"]
	remote = origin
	merge = refs/heads/hostel-enhancement
[branch "user-profile-restructure"]
	remote = origin
	merge = refs/heads/user-profile-restructure
[branch "library-book-loan-setting"]
	remote = origin
	merge = refs/heads/library-book-loan-setting
[branch "sprint-1-enhancement"]
	remote = origin
	merge = refs/heads/sprint-1-enhancement
[branch "fix/enrollment-test-case"]
	remote = origin
	merge = refs/heads/fix/enrollment-test-case
[branch "bugfix/sprint-1"]
	remote = origin
	merge = refs/heads/bugfix/sprint-1
[branch "bugfix/sprint-1-kimi"]
	remote = origin
	merge = refs/heads/bugfix/sprint-1-kimi
[branch "bugfix/sprint-1-noah"]
	remote = origin
	merge = refs/heads/bugfix/sprint-1-noah
[branch "bugfix/sprint-1-bed-api-filter"]
	remote = origin
	merge = refs/heads/bugfix/sprint-1-bed-api-filter
[branch "feature/conduct-grading"]
	remote = origin
	merge = refs/heads/feature/conduct-grading
[branch "master-awards"]
	remote = origin
	merge = refs/heads/master-awards
[branch "fix/sprint-1"]
	remote = origin
	merge = refs/heads/fix/sprint-1
[branch "feature/merit-demerit-settings"]
	remote = origin
	merge = refs/heads/feature/merit-demerit-settings
[branch "bugfix/sprint-1-feedback-fix-kimi"]
	remote = origin
	merge = refs/heads/bugfix/sprint-1-feedback-fix-kimi
[branch "bugfix/lucas/sprint-1"]
	remote = origin
	merge = refs/heads/bugfix/lucas/sprint-1
[branch "feature/counselling-record-CRUD"]
	remote = origin
	merge = refs/heads/feature/counselling-record-CRUD
[branch "comprehensive-assessment-category"]
	remote = origin
	merge = refs/heads/comprehensive-assessment-category
[branch "guardian-refactor"]
	remote = origin
	merge = refs/heads/guardian-refactor
[branch "comprehensive-assessment-questions"]
	remote = origin
	merge = refs/heads/comprehensive-assessment-questions
[branch "hotfix-noah"]
	remote = origin
	merge = refs/heads/hotfix-noah
[branch "bugfix/#176"]
	remote = origin
	merge = "refs/heads/bugfix/#176"
[branch "dev"]
	remote = origin
	merge = refs/heads/dev
[branch "feature/reward-punishments"]
	remote = origin
	merge = refs/heads/feature/reward-punishments
[branch "feature/reward-punishment-records"]
	remote = origin
	merge = refs/heads/feature/reward-punishment-records
[branch "comprehensive-assessment-results"]
	remote = origin
	merge = refs/heads/comprehensive-assessment-results
[branch "leadership-position"]
	remote = origin
	merge = refs/heads/leadership-position
[branch "feature/conduct-assign-setting"]
	remote = origin
	merge = refs/heads/feature/conduct-assign-setting
[branch "feature/class-subject"]
	remote = origin
	merge = refs/heads/feature/class-subject
[branch "timetable-period"]
	remote = origin
	merge = refs/heads/timetable-period
[branch "feature/contractor-CRUD"]
	remote = origin
	merge = refs/heads/feature/contractor-CRUD
[branch "add-student-name-number-filter"]
	remote = origin
	merge = refs/heads/add-student-name-number-filter
[branch "feature/wallet-enhancement"]
	remote = origin
	merge = refs/heads/feature/wallet-enhancement
[branch "feature/pos-terminal-keys-CRUD"]
	remote = origin
	merge = refs/heads/feature/pos-terminal-keys-CRUD
[branch "feature/permission"]
	remote = origin
	merge = refs/heads/feature/permission
[branch "ecommerce-product-userable"]
	remote = origin
	merge = refs/heads/ecommerce-product-userable
[branch "fix/add-show-to-pos-terminal-key"]
	remote = origin
	merge = refs/heads/fix/add-show-to-pos-terminal-key
[branch "ecommerce-report"]
	remote = origin
	merge = refs/heads/ecommerce-report
[branch "feature/role-permission"]
	remote = origin
	merge = refs/heads/feature/role-permission
[branch "ecommerce-product-enhancement"]
	remote = origin
	merge = refs/heads/ecommerce-product-enhancement
[branch "feature/wallet-index"]
	remote = origin
	merge = refs/heads/feature/wallet-index
[branch "feature/user-index"]
	remote = origin
	merge = refs/heads/feature/user-index
[branch "notification"]
	remote = origin
	merge = refs/heads/notification
[branch "library-bugs-fix"]
	remote = origin
	merge = refs/heads/library-bugs-fix
[branch "ecommerce-enhancement"]
	remote = origin
	merge = refs/heads/ecommerce-enhancement
[branch "feature/hostel-report-3.1"]
	remote = origin
	merge = refs/heads/feature/hostel-report-3.1
[branch "feature/lucas/library-report"]
	remote = origin
	merge = refs/heads/feature/lucas/library-report
[branch "isbn"]
	remote = origin
	merge = refs/heads/isbn
[branch "enhancements/ecommerce-cutoff"]
	remote = origin
	merge = refs/heads/enhancements/ecommerce-cutoff
[branch "feature/reset-password"]
	remote = origin
	merge = refs/heads/feature/reset-password
[branch "bank"]
	remote = origin
	merge = refs/heads/bank
[branch "bug-fix-sprint-4"]
	remote = origin
	merge = refs/heads/bug-fix-sprint-4
[branch "employee-category"]
	remote = origin
	merge = refs/heads/employee-category
[branch "guardian-fix"]
	remote = origin
	merge = refs/heads/guardian-fix
[branch "bugfix/library-report"]
	remote = origin
	merge = refs/heads/bugfix/library-report
[branch "student-details-report"]
	remote = origin
	merge = refs/heads/student-details-report
[branch "migration-library-member-employee"]
	remote = origin
	merge = refs/heads/migration-library-member-employee
[branch "migration-library-member-others"]
	remote = origin
	merge = refs/heads/migration-library-member-others
[branch "asset-rental"]
	remote = origin
	merge = refs/heads/asset-rental
[branch "feature/payex-service-redirect-url"]
	remote = origin
	merge = refs/heads/feature/payex-service-redirect-url
[branch "feature/guest-account"]
	remote = origin
	merge = refs/heads/feature/guest-account
[branch "hotfix/2025-02-10"]
	remote = origin
	merge = refs/heads/hotfix/2025-02-10
[branch "migration-hostel-reward-punishment"]
	remote = origin
	merge = refs/heads/migration-hostel-reward-punishment
[branch "staging/2025-02-13"]
	remote = origin
	merge = refs/heads/staging/2025-02-13
[branch "ecommerce-report-merchant-filter"]
	remote = origin
	merge = refs/heads/ecommerce-report-merchant-filter
[branch "hotfix/2025-02-17"]
	vscode-merge-base = origin/main
	remote = origin
	merge = refs/heads/hotfix/2025-02-17
[branch "add-direct-dependant-to-guardian-resource"]
	remote = origin
	merge = refs/heads/add-direct-dependant-to-guardian-resource
[branch "grading-framework-crud"]
	remote = origin
	merge = refs/heads/grading-framework-crud
[branch "staging/2025-02-27"]
	remote = origin
	merge = refs/heads/staging/2025-02-27
[branch "grading-framework-formula-fix"]
	remote = origin
	merge = refs/heads/grading-framework-formula-fix
[branch "fix/accounting-flow-changes"]
	remote = origin
	merge = refs/heads/fix/accounting-flow-changes
[branch "student-outstanding-balance-report-by-classes"]
	remote = origin
	merge = refs/heads/student-outstanding-balance-report-by-classes
[branch "product-resource-eager-loading"]
	remote = origin
	merge = refs/heads/product-resource-eager-loading
[branch "DO-NOT-MERGE-qas-temporary-data"]
	remote = origin
	merge = refs/heads/DO-NOT-MERGE-qas-temporary-data
[branch "leave-application-enhancements"]
	remote = origin
	merge = refs/heads/leave-application-enhancements
[branch "feature/qr-code-payment"]
	remote = origin
	merge = refs/heads/feature/qr-code-payment
[branch "exam-leftovers"]
	remote = origin
	merge = refs/heads/exam-leftovers
[branch "grading-framework-update-enhancements"]
	remote = origin
	merge = refs/heads/grading-framework-update-enhancements
[branch "feature/promotion-mark-CRUD"]
	remote = origin
	merge = refs/heads/feature/promotion-mark-CRUD
[branch "enhancement/add-year-to-semester-setting"]
	remote = origin
	merge = refs/heads/enhancement/add-year-to-semester-setting
[branch "api/duplicate-semester"]
	remote = origin
	merge = refs/heads/api/duplicate-semester
[branch "fix/conduct-mark-entry-after-deadline"]
	remote = origin
	merge = refs/heads/fix/conduct-mark-entry-after-deadline
[branch "permission-changes"]
	remote = origin
	merge = refs/heads/permission-changes
[branch "bugfix/substitute-management-and-period-sorting"]
	remote = origin
	merge = refs/heads/bugfix/substitute-management-and-period-sorting
[branch "staging/2025-04-20"]
	remote = origin
	merge = refs/heads/staging/2025-04-20
[pull]
	rebase = false
[branch "path-leave-application-individual-override"]
	remote = origin
	merge = refs/heads/path-leave-application-individual-override
[branch "exam-gross-average"]
	remote = origin
	merge = refs/heads/exam-gross-average
[branch "exam-module-changes"]
	remote = origin
	merge = refs/heads/exam-module-changes
[branch "bugfix/library-book-loan-report-employee-class-relation"]
	remote = origin
	merge = refs/heads/bugfix/library-book-loan-report-employee-class-relation
[branch "fix/attendance-mark-deduction-report"]
	remote = origin
	merge = refs/heads/fix/attendance-mark-deduction-report
[branch "issuelog-25-hostel-savings-see-transaction-balance"]
	remote = origin
	merge = refs/heads/issuelog-25-hostel-savings-see-transaction-balance
[branch "staging/2025-04-24"]
	remote = origin
	merge = refs/heads/staging/2025-04-24
[branch "report/teacher-attendance-report"]
	remote = origin
	merge = refs/heads/report/teacher-attendance-report
[branch "issue-124-employee-resigned-with-hostel-bed"]
	remote = origin
	merge = refs/heads/issue-124-employee-resigned-with-hostel-bed
[branch "class-attendance-taking-page-add-student-photo-employee-details"]
	remote = origin
	merge = refs/heads/class-attendance-taking-page-add-student-photo-employee-details
[branch "report/teacher-attendance"]
	remote = origin
	merge = refs/heads/report/teacher-attendance
[branch "issue-50-cocu-student-absent-report"]
	remote = origin
	merge = refs/heads/issue-50-cocu-student-absent-report
[branch "attendance-module-enhancements"]
	remote = origin
	merge = refs/heads/attendance-module-enhancements
[branch "fix/attendance-mark-deduction-styling"]
	remote = origin
	merge = refs/heads/fix/attendance-mark-deduction-styling
[branch "fix-get-attendance-period-for-student-bug"]
	remote = origin
	merge = refs/heads/fix-get-attendance-period-for-student-bug
[branch "tap-card-throw-error-if-inactive-card"]
	remote = origin
	merge = refs/heads/tap-card-throw-error-if-inactive-card
[branch "substitute-records-index-show-period-label-name"]
	remote = origin
	merge = refs/heads/substitute-records-index-show-period-label-name
[branch "staging/2025-05-07"]
	remote = origin
	merge = refs/heads/staging/2025-05-07
[branch "attendance-posting-direct-update-instead-of-delete-create"]
	remote = origin
	merge = refs/heads/attendance-posting-direct-update-instead-of-delete-create
[branch "attendance-posting-update-first-period-class-attendance-if-late"]
	remote = origin
	merge = refs/heads/attendance-posting-update-first-period-class-attendance-if-late
[branch "bug/library-loan-book-overdue-amount"]
	remote = origin
	merge = refs/heads/bug/library-loan-book-overdue-amount
[branch "bug/call_no_wildcard"]
	remote = origin
	merge = refs/heads/bug/call_no_wildcard
[branch "issue-83-auto-post-reward-punishment-records"]
	remote = origin
	merge = refs/heads/issue-83-auto-post-reward-punishment-records
[branch "staging/2025-05-08"]
	remote = origin
	merge = refs/heads/staging/2025-05-08
[branch "bug/product-index-enhancement"]
	remote = origin
	merge = refs/heads/bug/product-index-enhancement
[branch "issue-149-wallet-top-up-max-limit"]
	remote = origin
	merge = refs/heads/issue-149-wallet-top-up-max-limit
[branch "fix-substitute-record-query-bug"]
	remote = origin
	merge = refs/heads/fix-substitute-record-query-bug
[branch "student-statistic-report-added-class-code-column"]
	remote = origin
	merge = refs/heads/student-statistic-report-added-class-code-column
[branch "fix/attendance-mark-deduction-type-filters"]
	remote = origin
	merge = refs/heads/fix/attendance-mark-deduction-type-filters
[branch "timeslot-and-period-attendance-add-has-mark-deduction-column"]
	remote = origin
	merge = refs/heads/timeslot-and-period-attendance-add-has-mark-deduction-column
[branch "period-attendance-patch-script"]
	remote = origin
	merge = refs/heads/period-attendance-patch-script
[branch "exam-validate-subject-by-semester-setting"]
	remote = origin
	merge = refs/heads/exam-validate-subject-by-semester-setting
[branch "assign-class-to-student-using-new-latest-class-in-semester"]
	remote = origin
	merge = refs/heads/assign-class-to-student-using-new-latest-class-in-semester
[branch "report/hostel-report-saving-account"]
	remote = origin
	merge = refs/heads/report/hostel-report-saving-account
[branch "lucas/repository-fix"]
	remote = origin
	merge = refs/heads/lucas/repository-fix
[branch "repository-fix"]
	remote = origin
	merge = refs/heads/repository-fix
[branch "noah-fix-repo"]
	remote = origin
	merge = refs/heads/noah-fix-repo
[branch "fix-form-request"]
	remote = origin
	merge = refs/heads/fix-form-request
[branch "alvin-update-repository"]
	remote = origin
	merge = refs/heads/alvin-update-repository
[branch "ryan-repository-fix"]
	remote = origin
	merge = refs/heads/ryan-repository-fix
[branch "fix/repository-to-isset-kimi"]
	remote = origin
	merge = refs/heads/fix/repository-to-isset-kimi
[branch "jira-380-exam-uat-feedback"]
	remote = origin
	merge = refs/heads/jira-380-exam-uat-feedback
[branch "staging/2025-05-13"]
	remote = origin
	merge = refs/heads/staging/2025-05-13
[branch "JIRA-436-exam-changes"]
	remote = origin
	merge = refs/heads/JIRA-436-exam-changes
[branch "lucas-testing"]
	remote = origin
	merge = refs/heads/lucas-testing
[branch "exam-posting-prechecks-handle-is-exempted"]
	remote = origin
	merge = refs/heads/exam-posting-prechecks-handle-is-exempted
[branch "seed-exam-data"]
	remote = origin
	merge = refs/heads/seed-exam-data
[branch "staging/2025-05-19"]
	remote = origin
	merge = refs/heads/staging/2025-05-19
[branch "feature/enrollment-session-CRUD"]
	remote = origin
	merge = refs/heads/feature/enrollment-session-CRUD
[branch "add-guardian-resource-to-hostel-in-out-record"]
	remote = origin
	merge = refs/heads/add-guardian-resource-to-hostel-in-out-record
[branch "fix/update-validation-when-create-billing-doc"]
	remote = origin
	merge = refs/heads/fix/update-validation-when-create-billing-doc
[branch "JIRA-449-exam-module-changes-v2"]
	remote = origin
	merge = refs/heads/JIRA-449-exam-module-changes-v2
[branch "enrollment"]
	remote = origin
	merge = refs/heads/enrollment
[branch "enrollment-v2"]
	remote = origin
	merge = refs/heads/enrollment-v2
[branch "JIRA-448-grading-framework-changes"]
	remote = origin
	merge = refs/heads/JIRA-448-grading-framework-changes
[branch "feature/enrollment-migration"]
	remote = origin
	merge = refs/heads/feature/enrollment-migration
[branch "feature/enrollment-import-template"]
	remote = origin
	merge = refs/heads/feature/enrollment-import-template
[branch "feature/enrollment-import-and-save-APIs"]
	remote = origin
	merge = refs/heads/feature/enrollment-import-and-save-APIs
[branch "feature/enrollment-save-imported-excel-API"]
	remote = origin
	merge = refs/heads/feature/enrollment-save-imported-excel-API
[branch "enrollment-user"]
	remote = origin
	merge = refs/heads/enrollment-user
[branch "conduct-report"]
	remote = origin
	merge = refs/heads/conduct-report
[branch "patch-elective-courses"]
	remote = origin
	merge = refs/heads/patch-elective-courses
[branch "fix-wallet-trx-ref-no-unique"]
	remote = origin
	merge = refs/heads/fix-wallet-trx-ref-no-unique
[branch "feature/enrollment-setting-fee"]
	remote = origin
	merge = refs/heads/feature/enrollment-setting-fee
[branch "feature/function-to-determine-fees"]
	remote = origin
	merge = refs/heads/feature/function-to-determine-fees
[branch "fix/student-mark-deduction-report"]
	remote = origin
	merge = refs/heads/fix/student-mark-deduction-report
[branch "feature/enrollment-make-payment"]
	remote = origin
	merge = refs/heads/feature/enrollment-make-payment
[branch "exam-module-changes-v2"]
	remote = origin
	merge = refs/heads/exam-module-changes-v2
[branch "feature/enrollment-update-APIs"]
	remote = origin
	merge = refs/heads/feature/enrollment-update-APIs
[branch "script-remove-elective-subject-from-primary-class"]
	remote = origin
	merge = refs/heads/script-remove-elective-subject-from-primary-class
[branch "enrollment-enhancements"]
	remote = origin
	merge = refs/heads/enrollment-enhancements
[branch "feature/post-payment-enrollment-import"]
	remote = origin
	merge = refs/heads/feature/post-payment-enrollment-import
[branch "billing-doc-report-enhancement"]
	remote = origin
	merge = refs/heads/billing-doc-report-enhancement
[branch "issue-186-mark-deduction-report-exclude-inactive-students"]
	remote = origin
	merge = refs/heads/issue-186-mark-deduction-report-exclude-inactive-students
[branch "staging/2025-06-03"]
	remote = origin
	merge = refs/heads/staging/2025-06-03
[branch "issue-140-by-student-in-class-coco-and-english"]
	remote = origin
	merge = refs/heads/issue-140-by-student-in-class-coco-and-english
[branch "feature/import-validation-changes"]
	remote = origin
	merge = refs/heads/feature/import-validation-changes
[branch "enrollment-register-student-report"]
	remote = origin
	merge = refs/heads/enrollment-register-student-report
[branch "add-expiry-date-and-register-date"]
	remote = origin
	merge = refs/heads/add-expiry-date-and-register-date
[branch "feature/retry-enrollment-payment-API"]
	remote = origin
	merge = refs/heads/feature/retry-enrollment-payment-API
[branch "feature/enrollment-autocount-report"]
	remote = origin
	merge = refs/heads/feature/enrollment-autocount-report
[branch "staging/2025-06-05"]
	remote = origin
	merge = refs/heads/staging/2025-06-05
[branch "allow-take-class-attendance-5-mins-earlier"]
	remote = origin
	merge = refs/heads/allow-take-class-attendance-5-mins-earlier
[branch "enhancement/student-attendance-report"]
	remote = origin
	merge = refs/heads/enhancement/student-attendance-report
[branch "feature/enrollment-user-login-via-email"]
	remote = origin
	merge = refs/heads/feature/enrollment-user-login-via-email
[branch "exam-module-passing-marks"]
	remote = origin
	merge = refs/heads/exam-module-passing-marks
[branch "jira-180-examination-result-by-semester-class"]
	remote = origin
	merge = refs/heads/jira-180-examination-result-by-semester-class
[branch "examination-result-by-exam-report"]
	remote = origin
	merge = refs/heads/examination-result-by-exam-report
[branch "jira-183-exam-result-by-student"]
	remote = origin
	merge = refs/heads/jira-183-exam-result-by-student
[branch "feature/enrollment-update-marks-API"]
	remote = origin
	merge = refs/heads/feature/enrollment-update-marks-API
[branch "feature/enrollment-delete-API"]
	remote = origin
	merge = refs/heads/feature/enrollment-delete-API
[branch "feature/manual-payment-enrollment"]
	remote = origin
	merge = refs/heads/feature/manual-payment-enrollment
[branch "feature/enrollment-feedback"]
	remote = origin
	merge = refs/heads/feature/enrollment-feedback
[branch "feature/enrollment-feedback-2"]
	remote = origin
	merge = refs/heads/feature/enrollment-feedback-2
[branch "fix/unique-validation-for-exam-slip"]
	remote = origin
	merge = refs/heads/fix/unique-validation-for-exam-slip
[branch "staging/2025-06-10"]
	remote = origin
	merge = refs/heads/staging/2025-06-10
[branch "enrollment-student-report-new-exam-bands"]
	remote = origin
	merge = refs/heads/enrollment-student-report-new-exam-bands
[branch "exam-report-card-format-changes"]
	remote = origin
	merge = refs/heads/exam-report-card-format-changes
[branch "feature/get-summary-API"]
	remote = origin
	merge = refs/heads/feature/get-summary-API
[branch "added-with-position-in-standard-to-examination-result-by-class-report"]
	remote = origin
	merge = refs/heads/added-with-position-in-standard-to-examination-result-by-class-report
[branch "ai-rnd"]
	remote = origin
	merge = refs/heads/ai-rnd
[branch "feature/enrollment-siblings-update"]
	remote = origin
	merge = refs/heads/feature/enrollment-siblings-update
[branch "exam-result-data-entry-return-id-during-save-error"]
	remote = origin
	merge = refs/heads/exam-result-data-entry-return-id-during-save-error
[branch "examination-result-by-student-add-principal-name-homeroom-teacher-name"]
	remote = origin
	merge = refs/heads/examination-result-by-student-add-principal-name-homeroom-teacher-name
[branch "fix-examination-result-by-exam-report-header"]
	remote = origin
	merge = refs/heads/fix-examination-result-by-exam-report-header
[branch "staging/2025-06-13"]
	remote = origin
	merge = refs/heads/staging/2025-06-13
[branch "issue-180-students-by-primary-class-report-cosmetic-change"]
	remote = origin
	merge = refs/heads/issue-180-students-by-primary-class-report-cosmetic-change
[branch "issue-191-non-primary-student-by-semester-class-report-sorting"]
	remote = origin
	merge = refs/heads/issue-191-non-primary-student-by-semester-class-report-sorting
[branch "issue-188-cocu-student-statistic-report-exclude-inactive-student-class"]
	remote = origin
	merge = refs/heads/issue-188-cocu-student-statistic-report-exclude-inactive-student-class
[branch "exam-passing-rate-reports"]
	remote = origin
	merge = refs/heads/exam-passing-rate-reports
[branch "enhance-examination-result-by-class-report-to-check-is-exempted"]
	remote = origin
	merge = refs/heads/enhance-examination-result-by-class-report-to-check-is-exempted
[branch "lucas/fix-semester-class-saving-issue"]
	remote = origin
	merge = refs/heads/lucas/fix-semester-class-saving-issue
[branch "lucas/fix-sem-class-saving-issue"]
	remote = origin
	merge = refs/heads/lucas/fix-sem-class-saving-issue
[branch "exam-subject-analysis-report"]
	remote = origin
	merge = refs/heads/exam-subject-analysis-report
[branch "bugs/auto-assign-class-subject"]
	remote = origin
	merge = refs/heads/bugs/auto-assign-class-subject
[branch "staging/2025-06-16"]
	remote = origin
	merge = refs/heads/staging/2025-06-16
[branch "add-fallback-noto-serif-hk"]
	remote = origin
	merge = refs/heads/add-fallback-noto-serif-hk
[branch "fix/enhance-enrollment-admission-year"]
	remote = origin
	merge = refs/heads/fix/enhance-enrollment-admission-year
[branch "academy-subject-analysis-report-enhancement"]
	remote = origin
	merge = refs/heads/academy-subject-analysis-report-enhancement
[branch "exam-update-grading-framework-enhancement"]
	remote = origin
	merge = refs/heads/exam-update-grading-framework-enhancement
[branch "SKLEARN-474-api-guest-account-name-translation"]
	remote = origin
	merge = refs/heads/SKLEARN-474-api-guest-account-name-translation
[branch "staging/2025-06-18"]
	remote = origin
	merge = refs/heads/staging/2025-06-18
[branch "laravel-11-update"]
	remote = origin
	merge = refs/heads/laravel-11-update
[branch "fix/enrollment-user-changes"]
	remote = origin
	merge = refs/heads/fix/enrollment-user-changes
