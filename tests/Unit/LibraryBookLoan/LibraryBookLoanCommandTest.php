<?php

use App\Enums\LibraryBookLoanStatus;
use App\Enums\LibraryMemberType;
use App\Models\Book;
use App\Models\Config;
use App\Models\LibraryBookLoan;
use App\Models\LibraryMember;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Support\Facades\Artisan;

beforeEach(function () {
    app()->setLocale('en');

    $this->testLocale = app()->getLocale();

    $this->table = resolve(LibraryBookLoan::class)->getTable();

    $config = [
        Config::LIBRARY_BORROW_LIMIT_EMPLOYEE => 3,
        Config::LIBRARY_BORROW_LIMIT_STUDENT => 2,
        Config::LIBRARY_BORROW_LIMIT_LIBRARIAN => 5,
        Config::LIBRARY_BORROW_LIMIT_OTHER => 4,

        Config::BORROW_WITH_UNRETURNED_BOOK_EMPLOYEE => false,
        Config::BORROW_WITH_UNRETURNED_BOOK_STUDENT => true,
        Config::BORROW_WITH_UNRETURNED_BOOK_LIBRARIAN => false,
        Config::BORROW_WITH_UNRETURNED_BOOK_OTHER => false,

        Config::LIBRARY_FINE_PER_DAY_EMPLOYEE => 0.1,
        Config::LIBRARY_FINE_PER_DAY_STUDENT => 0.2,
        Config::LIBRARY_FINE_PER_DAY_LIBRARIAN => 0.1,
        Config::LIBRARY_FINE_PER_DAY_OTHER => 0,
    ];

    foreach ($config as $key => $value) {
        Config::factory()->create([
            'key' => $key,
            'value' => $value
        ]);
    }
});

test('book:loan-overdue-amount', function () {
    Carbon::setTestNow('2025-05-03 16:01:00'); // UTC+0
    $books = Book::factory(2)->create();
    $member_student = LibraryMember::factory()->create([
        'type' => LibraryMemberType::STUDENT
    ]);
    $member_employee = LibraryMember::factory()->create([
        'type' => LibraryMemberType::EMPLOYEE
    ]);

    $student_overdue_book_loans = LibraryBookLoan::factory(2)->state(new Sequence(
        [
            'member_id' => $member_student->id,
            'book_id' => $books[0]->id,
            'due_date' => '2025-05-02',
            'loan_status' => LibraryBookLoanStatus::BORROWED,
            'penalty_overdue_amount' => 0,
            'penalty_total_fine_amount' => 0
        ],
        [
            'member_id' => $member_student->id,
            'book_id' => $books[0]->id,
            'due_date' => '2025-05-01',
            'loan_status' => LibraryBookLoanStatus::BORROWED,
            'penalty_overdue_amount' => 0,
            'penalty_total_fine_amount' => 0
        ],
    ))->create();

    $student_not_overdue_book_loan = LibraryBookLoan::factory()->create([
        'member_id' => $member_student->id,
        'book_id' => $books[0]->id,
        'due_date' => now()->addDays(2),
        'loan_status' => LibraryBookLoanStatus::BORROWED,
        'penalty_overdue_amount' => 0,
        'penalty_total_fine_amount' => 0
    ]);

    $student_overdue_book_loan_status_lost = LibraryBookLoan::factory()->create([
        'member_id' => $member_student->id,
        'book_id' => $books[0]->id,
        'due_date' => now()->subDays(2),
        'loan_status' => LibraryBookLoanStatus::LOST,
        'penalty_overdue_amount' => 0,
        'penalty_total_fine_amount' => 0
    ]);

    $employee_overdue_book_loans = LibraryBookLoan::factory(2)->state(new Sequence(
        [
            'member_id' => $member_employee->id,
            'book_id' => $books[0]->id,
            'due_date' => '2025-05-02',
            'loan_status' => LibraryBookLoanStatus::BORROWED,
            'penalty_overdue_amount' => 0,
            'penalty_total_fine_amount' => 0
        ],
        [
            'member_id' => $member_employee->id,
            'book_id' => $books[0]->id,
            'due_date' => '2025-05-01',
            'loan_status' => LibraryBookLoanStatus::BORROWED,
            'penalty_overdue_amount' => 0,
            'penalty_total_fine_amount' => 0
        ],
    ))->create();

    Artisan::call('book:loan-overdue-amount');

    //student overdue amount and total fine amount is updated
    $this->assertDatabaseHas($this->table, [
        'id' => $student_overdue_book_loans[0]->id,
        'due_date' => '2025-05-02',
        'loan_status' => LibraryBookLoanStatus::BORROWED,
        'penalty_overdue_amount' => 0.4,
        'penalty_total_fine_amount' => 0.4
    ]);

    $this->assertDatabaseHas($this->table, [
        'id' => $student_overdue_book_loans[1]->id,
        'penalty_overdue_amount' => 0.6,
        'penalty_total_fine_amount' => 0.6
    ]);

    //student loan not overdue is remain 0
    $this->assertDatabaseHas($this->table, [
        'id' => $student_not_overdue_book_loan->id,
        'penalty_overdue_amount' => 0,
        'penalty_total_fine_amount' => 0
    ]);

    //student loan overdue but status LOST remain 0
    $this->assertDatabaseHas($this->table, [
        'id' => $student_overdue_book_loan_status_lost->id,
        'penalty_overdue_amount' => 0,
        'penalty_total_fine_amount' => 0
    ]);

    //employee overdue amount and total fine amount is updated
    $this->assertDatabaseHas($this->table, [
        'id' => $employee_overdue_book_loans[0]->id,
        'due_date' => '2025-05-02',
        'loan_status' => LibraryBookLoanStatus::BORROWED,
        'penalty_overdue_amount' => 0.2,
        'penalty_total_fine_amount' => 0.2
    ]);

    $this->assertDatabaseHas($this->table, [
        'id' => $employee_overdue_book_loans[1]->id,
        'penalty_overdue_amount' => 0.3,
        'penalty_total_fine_amount' => 0.3
    ]);
});


