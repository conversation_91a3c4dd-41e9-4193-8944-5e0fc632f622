<?php

use App\Enums\EnrollmentPaymentStatus;
use App\Enums\EnrollmentStatus;
use App\Enums\Gender;
use App\Http\Resources\CurrencyResource;
use App\Models\BillingDocument;
use App\Models\Course;
use App\Models\Currency;
use App\Models\Enrollment;
use App\Models\EnrollmentExam;
use App\Models\EnrollmentSession;
use App\Models\Grade;
use App\Models\Payment;
use App\Models\Subject;
use App\Services\EnrollmentSessionService;
use Carbon\Carbon;
use Database\Seeders\CurrencySeeder;
use Database\Seeders\InternationalizationSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Pagination\LengthAwarePaginator;

beforeEach(function () {
    $this->seed(InternationalizationSeeder::class);

    $this->enrollmentSessionService = app()->make(EnrollmentSessionService::class);
});

test('getAllPaginatedEnrollmentSessions', function () {
    $enrollment_sessions = EnrollmentSession::factory(2)->create();

    $this->mock(EnrollmentSessionService::class, function (\Mockery\MockInterface $mock) use ($enrollment_sessions) {
        $mock->shouldReceive('getAllPaginatedEnrollmentSessions')
            ->once()
            ->andReturn(new LengthAwarePaginator($enrollment_sessions, 2, 1));
    });

    $response = app()->make(EnrollmentSessionService::class)
        ->getAllPaginatedEnrollmentSessions()
        ->toArray();

    expect($response['data'])->toHaveCount(2)
        ->toHaveKey('0.id', $enrollment_sessions[0]->id)
        ->toHaveKey('1.id', $enrollment_sessions[1]->id);
});

test('getAllEnrollmentSessions', function () {
    $enrollment_sessions = EnrollmentSession::factory(2)->create();

    $this->mock(EnrollmentSessionService::class, function (\Mockery\MockInterface $mock) use ($enrollment_sessions) {
        $mock->shouldReceive('getAllEnrollmentSessions')
            ->once()
            ->andReturn($enrollment_sessions);
    });

    $response = app()->make(EnrollmentSessionService::class)
        ->getAllEnrollmentSessions()
        ->toArray();

    expect($response)->toHaveCount(2)
        ->toHaveKey('0.id', $enrollment_sessions[0]->id)
        ->toHaveKey('1.id', $enrollment_sessions[1]->id);
});

test('createEnrollmentSession without fee_assignment_settings', function () {
    $subject_1 = Subject::factory()->create();
    $subject_2 = Subject::factory()->create();

    $course = Course::factory()->create();
    $grade = Grade::factory()->create();

    $data = [
        'name' => 'Test Enrollment Session',
        'from_date' => now()->toDateString(),
        'to_date' => now()->addDays(30)->toDateString(),
        'code' => 'TEST_AA_SESSION',
        'is_active' => true,
        'course_id' => $course->id,
        'subject_ids' => [$subject_1->id, $subject_2->id],
        'admission_year' => 2023,
        'admission_grade_id' => $grade->id,
    ];

    $response = $this->enrollmentSessionService->createEnrollmentSession($data);

    expect($response)->toBeInstanceOf(EnrollmentSession::class)
        ->name->toBe($data['name'])
        ->from_date->toBe($data['from_date'])
        ->to_date->toBe($data['to_date'])
        ->code->toBe($data['code'])
        ->is_active->toBe($data['is_active'])
        ->course_id->toBe($data['course_id'])
        ->admission_year->toBe($data['admission_year'])
        ->admission_grade_id->toBe($data['admission_grade_id']);

    expect($response->examSubjects)->toHaveCount(2);

    $this->assertDatabaseHas('enrollment_sessions', [
        'name' => $data['name'],
        'from_date' => $data['from_date'],
        'to_date' => $data['to_date'],
        'code' => $data['code'],
        'is_active' => $data['is_active'],
        'course_id' => $data['course_id'],
        'admission_year' => $data['admission_year'],
        'admission_grade_id' => $data['admission_grade_id'],
        'fee_assignment_settings' => null, // No fee assignment settings
    ]);

    $this->assertDatabaseHas('enrollment_session_exam_subject', [
        'enrollment_session_id' => $response->id,
        'subject_id' => $subject_1->id,
    ]);

    $this->assertDatabaseHas('enrollment_session_exam_subject', [
        'enrollment_session_id' => $response->id,
        'subject_id' => $subject_2->id,
    ]);
});

test('createEnrollmentSession with fee_assignment_settings', function () {
    $subject_1 = Subject::factory()->create();
    $subject_2 = Subject::factory()->create();

    $course = Course::factory()->create();
    $grade = Grade::factory()->create();

    // temp fee assignment settings
    $temp_fee_assignment_settings = [
        [
            'conditions' => null,
            'outcome' => [
                'product_id' => 1,
                'amount' => 1000,
                'period' => '2024-01-01',
            ]
        ],
        [
            'conditions' => [
                [
                    'field' => 'is_foreigner',
                    'operator' => '=',
                    'value' => false,
                ],
            ],
            'outcome' => [
                'product_id' => 2,
                'amount' => 2000,
                'period' => '2024-02-01',
            ]
        ]
    ];

    $data = [
        'name' => 'Test Enrollment Session',
        'from_date' => now()->toDateString(),
        'to_date' => now()->addDays(30)->toDateString(),
        'code' => 'TEST_AA_SESSION',
        'is_active' => true,
        'course_id' => $course->id,
        'subject_ids' => [$subject_1->id, $subject_2->id],
        'admission_year' => 2023,
        'admission_grade_id' => $grade->id,
        'fee_assignment_settings' => $temp_fee_assignment_settings,
    ];

    $response = $this->enrollmentSessionService->createEnrollmentSession($data);

    expect($response)->toBeInstanceOf(EnrollmentSession::class)
        ->name->toBe($data['name'])
        ->from_date->toBe($data['from_date'])
        ->to_date->toBe($data['to_date'])
        ->code->toBe($data['code'])
        ->is_active->toBe($data['is_active'])
        ->course_id->toBe($data['course_id'])
        ->fee_assignment_settings->toEqual($temp_fee_assignment_settings)
        ->admission_year->toBe($data['admission_year'])
        ->admission_grade_id->toBe($data['admission_grade_id']);

    expect($response->examSubjects)->toHaveCount(2);

    $this->assertDatabaseHas('enrollment_sessions', [
        'name' => $data['name'],
        'from_date' => $data['from_date'],
        'to_date' => $data['to_date'],
        'code' => $data['code'],
        'is_active' => $data['is_active'],
        'course_id' => $data['course_id'],
        'admission_year' => $data['admission_year'],
        'admission_grade_id' => $data['admission_grade_id'],
        'fee_assignment_settings' => json_encode($temp_fee_assignment_settings),
    ]);

    $this->assertDatabaseHas('enrollment_session_exam_subject', [
        'enrollment_session_id' => $response->id,
        'subject_id' => $subject_1->id,
    ]);

    $this->assertDatabaseHas('enrollment_session_exam_subject', [
        'enrollment_session_id' => $response->id,
        'subject_id' => $subject_2->id,
    ]);
});

test('updateEnrollmentSession', function () {
    $old_subject_1 = Subject::factory()->create();
    $new_subject_1 = Subject::factory()->create();
    $new_subject_2 = Subject::factory()->create();

    $course = Course::factory()->create();
    $grade = Grade::factory()->create();

    $enrollment_session = EnrollmentSession::factory()->create();
    $enrollment_session->examSubjects()->attach($old_subject_1);

    // temp fee assignment settings
    $temp_fee_assignment_settings = [
        [
            'conditions' => null,
            'outcome' => [
                'product_id' => 1,
                'amount' => 1000,
                'period' => '2024-01-01',
            ]
        ],
        [
            'conditions' => [
                [
                    'field' => 'is_foreigner',
                    'operator' => '=',
                    'value' => true,
                ],
            ],
            'outcome' => [
                'product_id' => 2,
                'amount' => 2000,
                'period' => '2024-02-01',
            ]
        ]
    ];

    $data = [
        'name' => 'Updated Enrollment Session',
        'from_date' => now()->toDateString(),
        'to_date' => now()->addDays(30)->toDateString(),
        'code' => 'TEST_AA_SESSION',
        'is_active' => true,
        'course_id' => $course->id,
        'subject_ids' => [$new_subject_1->id, $new_subject_2->id],
        'admission_year' => 2023,
        'admission_grade_id' => $grade->id,
        'fee_assignment_settings' => $temp_fee_assignment_settings,
    ];

    $response = $this->enrollmentSessionService->updateEnrollmentSession($enrollment_session, $data);

    $response->refresh();

    expect($response)->toBeInstanceOf(EnrollmentSession::class)
        ->name->toBe($data['name'])
        ->from_date->toBe($data['from_date'])
        ->to_date->toBe($data['to_date'])
        ->code->toBe($data['code'])
        ->is_active->toBe($data['is_active'])
        ->course_id->toBe($data['course_id'])
        ->fee_assignment_settings->toEqual($temp_fee_assignment_settings)
        ->admission_year->toBe($data['admission_year'])
        ->admission_grade_id->toBe($data['admission_grade_id']);

    expect($response->examSubjects)->toHaveCount(2);

    $this->assertDatabaseHas('enrollment_sessions', [
        'name' => $data['name'],
        'from_date' => $data['from_date'],
        'to_date' => $data['to_date'],
        'code' => $data['code'],
        'is_active' => $data['is_active'],
        'course_id' => $data['course_id'],
        'admission_year' => $data['admission_year'],
        'admission_grade_id' => $data['admission_grade_id'],
        'fee_assignment_settings' => json_encode($temp_fee_assignment_settings),
    ]);

    $this->assertDatabaseHas('enrollment_session_exam_subject', [
        'enrollment_session_id' => $response->id,
        'subject_id' => $new_subject_1->id,
    ]);

    $this->assertDatabaseHas('enrollment_session_exam_subject', [
        'enrollment_session_id' => $response->id,
        'subject_id' => $new_subject_2->id,
    ]);

    // old subject should be removed
    $this->assertDatabaseMissing('enrollment_session_exam_subject', [
        'enrollment_session_id' => $response->id,
        'subject_id' => $old_subject_1->id,
    ]);
});

test('deleteEnrollmentSession', function () {
    $enrollment_session = EnrollmentSession::factory()->create();

    $this->enrollmentSessionService->deleteEnrollmentSession($enrollment_session);

    $this->assertDatabaseMissing('enrollment_sessions', [
        'id' => $enrollment_session->id,
    ]);
});

test('deleteEnrollmentSession failed because already attached with subjects', function () {
    $enrollment_session = EnrollmentSession::factory()->create();
    $subject = Subject::factory()->create();

    $enrollment_session->examSubjects()->attach($subject);

    expect(function () use ($enrollment_session) {
        $this->enrollmentSessionService->deleteEnrollmentSession($enrollment_session);
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(5002);
    }, __('system_error.5002'));
});

test('getFeeSettingConditions', function () {
    $conditions = $this->enrollmentSessionService->getFeeSettingConditions();

    expect($conditions)->toBeArray()
        ->toHaveCount(2);

    expect($conditions[0]['value'])->toBe('is_foreigner');
    expect($conditions[0]['label'])->toBe('Is Foreigner');
    expect($conditions[0]['options'])->toBeArray();
    expect($conditions[1]['options'][0]['value'])->toBe(true);
    expect($conditions[1]['options'][0]['label'])->toBe('Yes');
    expect($conditions[1]['options'][1]['value'])->toBe(false);
    expect($conditions[1]['options'][1]['label'])->toBe('No');

    expect($conditions[1]['value'])->toBe('is_hostel');
    expect($conditions[1]['label'])->toBe('Staying in hostel');
    expect($conditions[1]['options'])->toBeArray();
    expect($conditions[1]['options'][0]['value'])->toBe(true);
    expect($conditions[1]['options'][0]['label'])->toBe('Yes');
    expect($conditions[1]['options'][1]['value'])->toBe(false);
    expect($conditions[1]['options'][1]['label'])->toBe('No');
});

test('getSummary', function () {
    $this->seed(CurrencySeeder::class);

    Carbon::setTestNow(Carbon::parse('2025-06-11 12:00:00'));

    $session = EnrollmentSession::factory()->create();
    $other_session = EnrollmentSession::factory()->create();

    $enrollments = Enrollment::factory(5)->state(new Sequence(
        [
            'enrollment_session_id' => $session->id,
            'created_at' => Carbon::now()->subHours(12), // within 1 day
            'enrollment_status' => EnrollmentStatus::APPROVED->value,
            'gender' => Gender::MALE->value,
            'is_hostel' => true,
            'is_foreigner' => false,
            'payment_status' => EnrollmentPaymentStatus::PAID->value,
        ],
        [
            'enrollment_session_id' => $session->id,
            'created_at' => Carbon::now()->subDays(3), // within 7 days
            'enrollment_status' => EnrollmentStatus::APPROVED->value,
            'gender' => Gender::FEMALE->value,
            'is_hostel' => true,
            'is_foreigner' => false,
            'payment_status' => EnrollmentPaymentStatus::PAID->value,
        ],
        [
            'enrollment_session_id' => $session->id,
            'created_at' => Carbon::now()->subDays(15), // within 30 days
            'enrollment_status' => EnrollmentStatus::APPROVED->value,
            'gender' => Gender::FEMALE->value,
            'is_hostel' => true,
            'is_foreigner' => false,
            'payment_status' => EnrollmentPaymentStatus::UNPAID->value,
        ],
        [
            'enrollment_session_id' => $session->id,
            'created_at' => Carbon::now()->subDays(15), // within 30 days
            'enrollment_status' => EnrollmentStatus::REJECTED->value,
            'gender' => Gender::FEMALE->value,
            'is_hostel' => false,
            'is_foreigner' => false,
            'payment_status' => EnrollmentPaymentStatus::UNPAID->value,
        ],
        [
            'enrollment_session_id' => $other_session->id, // unrelated , will be excluded
        ],
    ))->create();

    $enrollment_exams = EnrollmentExam::factory(3)->state(new Sequence(
        [
            'enrollment_id' => $enrollments[0]->id, // 2 exams for the first enrollment
        ],
        [
            'enrollment_id' => $enrollments[0]->id,
        ],
        [
            'enrollment_id' => $enrollments[1]->id,
        ],
    ))->create();


    $invoice1 = BillingDocument::factory()->create([
        'bill_to_id' => $enrollments[0]->id,
        'bill_to_type' => Enrollment::class,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
        'amount_after_tax' => 1500.00,
    ]);

    $invoice2 = BillingDocument::factory()->create([
        'bill_to_id' => $enrollments[1]->id,
        'bill_to_type' => Enrollment::class,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PARTIAL,
        'amount_after_tax' => 1000.50,
    ]);

    $unpaid_invoice = BillingDocument::factory()->create([
        'bill_to_id' => $enrollments[2]->id,
        'bill_to_type' => Enrollment::class,
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
    ]);

    Payment::factory()->create([
        'billing_document_id' => $invoice1->id,
        'amount_received' => 1500.00,
    ]);

    Payment::factory()->create([
        'billing_document_id' => $invoice2->id,
        'amount_received' => 750.50, // still left 1000.50 - 750.50 = 250.00 unpaid
    ]);

    $summary = $this->enrollmentSessionService->getSummary($session);

    expect($summary)->toEqual(
        [
            'total_applications_submitted' => 4, // only 4 because one enrollment is from another session
            'by_submission_date' => [
                [
                    'label' => 'One Day Ago',
                    'value' => 1,
                ],
                [
                    'label' => '7 Days Ago',
                    'value' => 1,
                ],
                [
                    'label' => '30 Days Ago',
                    'value' => 2,
                ],
            ],
            'by_exam_requirement' => [
                [
                    'label' => 'Exam',
                    'value' => 2, // 2 enrollments with exams // $enrollments[0] (2 exams) and $enrollments[1] (1 exam)
                ],
                [
                    'label' => 'Without Exam',
                    'value' => 2, // 2 enrollments without exams
                ],
            ],
            'by_enrollment_status' => [
                [
                    'label' => 'Approved',
                    'value' => 3, // 3 approved enrollments
                ],
                [
                    'label' => 'Rejected',
                    'value' => 1, // 1 rejected enrollment
                ],
                [
                    'label' => 'Shortlisted',
                    'value' => 0, // No shortlisted enrollments in this test
                ],
            ],
            'total_confirmed_students' => 3, // Approved enroll
            'by_gender' => [ // APPROVED
                [
                    'label' => 'Male',
                    'value' => 1,
                ],
                [
                    'label' => 'Female',
                    'value' => 2,
                ],
            ],
            'by_hostel' => [ // APPROVED
                [
                    'label' => 'Hostel',
                    'value' => 3, // 3 students staying in hostel
                ],
                [
                    'label' => 'Non Hostel',
                    'value' => 0, // No non-hostel students in this test
                ],
            ],
            'by_citizenship' => [ // APPROVED
                [
                    'label' => 'Foreigner',
                    'value' => 0,
                ],
                [
                    'label' => 'Local',
                    'value' => 3, // 3 local students
                ],
            ],
            'total_fee_collection' => '2,250.50', // 1500.00 + 750.50
            'by_full_payment_status' => [
                [
                    'label' => 'Paid',
                    'value' => 2, // 2 PAID
                ],
                [
                    'label' => 'Unpaid',
                    'value' => 2, // 2 Unpaid
                ],
                [
                    'label' => 'Pending',
                    'value' => 0, // No pending payments
                ],
            ],
            'currency' => resourceToArray(new CurrencyResource(Currency::first())),
        ]
    );
});

