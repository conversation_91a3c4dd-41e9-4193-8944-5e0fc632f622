<?php

use App\Enums\EnrollmentPaymentStatus;
use App\Enums\EnrollmentStatus;
use App\Enums\Gender;
use App\Models\Country;
use App\Models\Enrollment;
use App\Models\EnrollmentGuardian;
use App\Models\Grade;
use App\Models\Race;
use App\Models\Religion;
use App\Models\State;
use App\Models\User;
use App\Repositories\EnrollmentRepository;
use Database\Seeders\InternationalizationSeeder;

beforeEach(function () {
    $this->seed(InternationalizationSeeder::class);
    $this->enrollmentRepository = resolve(EnrollmentRepository::class);

    app()->setLocale('en');

    $this->locale = app()->getLocale();
});

test('getModelClass()', function () {
    $response = $this->enrollmentRepository->getModelClass();

    expect($response)->toEqual(Enrollment::class);
});


test('getAll()', function () {
    $first_enrollment = Enrollment::factory()->create([
        'name->en' => '<PERSON>',
        'is_hostel' => true,
        'is_foreigner' => true,
        'expiry_date' => '2025-01-20',
        'registration_date' => '2025-01-01',
    ]);

    $second_enrollment = Enrollment::factory()->create([
        'name->en' => 'AA Name'
    ]);

    $third_enrollment = Enrollment::factory()->create([
        'name->en' => 'BBO Name'
    ]);

    $grade = Grade::factory()->create();
    $fourth_enrollment = Enrollment::factory()->create([
        'admission_grade_id' => $grade->id,
    ]);

    $birthplace_country = Country::factory()->create();
    $fifth_enrollment = Enrollment::factory()->create([
        'birthplace' => 'Kajang',
    ]);

    $nationality = Country::factory()->create();
    $sixth_enrollment = Enrollment::factory()->create([
        'nationality_id' => $nationality->id,
    ]);

    $race = Race::factory()->create();
    $seventh_enrollment = Enrollment::factory()->create([
        'race_id' => $race->id,
    ]);

    $religion = Religion::factory()->create();
    $eighth_enrollment = Enrollment::factory()->create([
        'religion_id' => $religion->id,
    ]);

    $birth_cert_number = fake()->uuid();
    $nineth_enrollment = Enrollment::factory()->create([
        'birth_cert_number' => $birth_cert_number,
    ]);

    $nric = fake()->uuid();
    $tenth_enrollment = Enrollment::factory()->create([
        'nric' => $nric,
    ]);

    $gender = Gender::FEMALE->value;
    $eleventh_enrollment = Enrollment::factory()->create([
        'gender' => $gender,
    ]);

    $thirteen_enrollment = Enrollment::factory()->create([
        'admission_year' => 2035,
    ]);

    $passport_number = fake()->uuid();
    $fourteen_enrollment = Enrollment::factory()->create([
        'passport_number' => $passport_number,
    ]);

    $state = State::factory()->create();
    $fifteen_enrollment = Enrollment::factory()->create([
        'state_id' => $state->id,
    ]);

    $country = Country::factory()->create();
    $sixteen_enrollment = Enrollment::factory()->create([
        'country_id' => $country->id,
    ]);

    // =================================================================================

    // Filter by admission_year = 2035
    $response = $this->enrollmentRepository->getAll(['admission_year' => 2035])->toArray();

    expect($response)->toHaveCount(1)->toEqual([$thirteen_enrollment->toArray()]);

    // =================================================================================

    // Filter by admission_grade_id = $grade->id
    $response = $this->enrollmentRepository->getAll(['admission_grade_id' => $grade->id])->toArray();

    expect($response)->toHaveCount(1)->toEqual([$fourth_enrollment->toArray()]);

    // =================================================================================

    // Filter by name = John Jones
    $response = $this->enrollmentRepository->getAll(['name' => 'John Jones'])->toArray();

    expect($response)->toHaveCount(1)->toEqual([$first_enrollment->toArray()]);

    // Filter by name = BBO Name
    $response = $this->enrollmentRepository->getAll(['name' => 'BBO Name'])->toArray();

    expect($response)->toHaveCount(1)->toEqual([$third_enrollment->toArray()]);

    // Filter by non-existing name = Meh
    $response = $this->enrollmentRepository->getAll(['name' => 'Meh'])->toArray();

    expect($response)->toHaveCount(0)->toBeEmpty();

    // =================================================================================

    // Filter by nric = $nric
    $response = $this->enrollmentRepository->getAll(['nric' => $nric])->toArray();

    expect($response)->toHaveCount(1)->toEqual([$tenth_enrollment->toArray()]);

    // =================================================================================

    // Filter by passport_number = $passport_number
    $response = $this->enrollmentRepository->getAll(['passport_number' => $passport_number])->toArray();

    expect($response)->toHaveCount(1)->toEqual([$fourteen_enrollment->toArray()]);

    // =================================================================================

    // Filter by nationality_id = $nationality->id
    $response = $this->enrollmentRepository->getAll(['nationality_id' => $nationality->id])->toArray();

    expect($response)->toHaveCount(1)->toEqual([$sixth_enrollment->toArray()]);

    // =================================================================================

    // Filter by gender = $gender
    $response = $this->enrollmentRepository->getAll(['gender' => $gender])->toArray();

    expect($response)->toHaveCount(1)->toEqual([$eleventh_enrollment->toArray()]);

    // =================================================================================


    // Filter by birth_cert_number = $birth_cert_number
    $response = $this->enrollmentRepository->getAll(['birth_cert_number' => $birth_cert_number])->toArray();

    expect($response)->toHaveCount(1)->toEqual([$nineth_enrollment->toArray()]);

    // =================================================================================

    // Filter by race_id = $race->id
    $response = $this->enrollmentRepository->getAll(['race_id' => $race->id])->toArray();

    expect($response)->toHaveCount(1)->toEqual([$seventh_enrollment->toArray()]);

    // =================================================================================

    // Filter by religion_id = $religion->id
    $response = $this->enrollmentRepository->getAll(['religion_id' => $religion->id])->toArray();

    expect($response)->toHaveCount(1)->toEqual([$eighth_enrollment->toArray()]);

    // =================================================================================

    // Filter by state_id = $state->id
    $response = $this->enrollmentRepository->getAll(['state_id' => $state->id])->toArray();

    expect($response)->toHaveCount(1)->toEqual([$fifteen_enrollment->toArray()]);

    // =================================================================================

    // Filter by country_id = $country->id
    $response = $this->enrollmentRepository->getAll(['country_id' => $country->id])->toArray();

    expect($response)->toHaveCount(1)->toEqual([$sixteen_enrollment->toArray()]);

    // Filter by is_hostel = true
    $response = $this->enrollmentRepository->getAll(['is_hostel' => true])->toArray();

    expect($response)->toHaveCount(1)->toEqual([$first_enrollment->toArray()]);

    // Filter by is_foreigner = true
    $response = $this->enrollmentRepository->getAll(['is_foreigner' => true])->toArray();

    expect($response)->toHaveCount(1)->toEqual([$first_enrollment->toArray()]);

    // Filter by expiry_date = 2025-01-20
    $response = $this->enrollmentRepository->getAll(['expiry_date' => '2025-01-20'])->toArray();

    expect($response)->toHaveCount(1)->toEqual([$first_enrollment->toArray()]);

    // Filter by registration_date = 2025-01-01
    $response = $this->enrollmentRepository->getAll(['registration_date' => '2025-01-01'])->toArray();

    expect($response)->toHaveCount(1)->toEqual([$first_enrollment->toArray()]);

    // =================================================================================
    // =================================================================================
    // =================================================================================

    // Sort by id asc
    $response = $this->enrollmentRepository->getAll([
        'order_by' => ['id' => 'asc'],
    ])->toArray();

    expect($response)->sequence(
        fn($enrollment) => $enrollment->id->toBe($first_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($second_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($third_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($fourth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($fifth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($sixth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($seventh_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($eighth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($nineth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($tenth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($eleventh_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($thirteen_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($fourteen_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($fifteen_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($sixteen_enrollment->id),
    );

    // Sort by id desc
    $response = $this->enrollmentRepository->getAll([
        'order_by' => ['id' => 'desc'],
    ])->toArray();

    expect($response)->sequence(
        fn($enrollment) => $enrollment->id->toBe($sixteen_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($fifteen_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($fourteen_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($thirteen_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($eleventh_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($tenth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($nineth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($eighth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($seventh_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($sixth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($fifth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($fourth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($third_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($second_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($first_enrollment->id),
    );

    // Sort by nationality_id asc
    $response = $this->enrollmentRepository->getAll([
        'order_by' => ['nationality_id' => 'asc'],
    ])->toArray();

    expect($response)->sequence(
        fn($enrollment) => $enrollment->nationality_id->toBe($first_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($second_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($third_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($fourth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($fifth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($sixth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($seventh_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($eighth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($nineth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($tenth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($eleventh_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($thirteen_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($fourteen_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($fifteen_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($sixteen_enrollment->nationality_id),
    );

    // Sort by nationality_id desc
    $response = $this->enrollmentRepository->getAll([
        'order_by' => ['nationality_id' => 'desc'],
    ])->toArray();

    expect($response)->sequence(
        fn($enrollment) => $enrollment->nationality_id->toBe($sixteen_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($fifteen_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($fourteen_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($thirteen_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($eleventh_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($tenth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($nineth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($eighth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($seventh_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($sixth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($fifth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($fourth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($third_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($second_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($first_enrollment->nationality_id),
    );
});

test('getAllPaginated()', function () {
    $first_enrollment = Enrollment::factory()->create([
        'name->en' => 'John Jones'
    ]);

    $second_enrollment = Enrollment::factory()->create([
        'name->en' => 'AA Name'
    ]);

    $third_enrollment = Enrollment::factory()->create([
        'name->en' => 'BBO Name'
    ]);

    $grade = Grade::factory()->create();
    $fourth_enrollment = Enrollment::factory()->create([
        'admission_grade_id' => $grade->id,
    ]);

    $fifth_enrollment = Enrollment::factory()->create([
        'birthplace' => 'Kajang',
    ]);

    $nationality = Country::factory()->create();
    $sixth_enrollment = Enrollment::factory()->create([
        'nationality_id' => $nationality->id,
    ]);

    $race = Race::factory()->create();
    $seventh_enrollment = Enrollment::factory()->create([
        'race_id' => $race->id,
    ]);

    $religion = Religion::factory()->create();
    $eighth_enrollment = Enrollment::factory()->create([
        'religion_id' => $religion->id,
    ]);

    $birth_cert_number = fake()->uuid();
    $nineth_enrollment = Enrollment::factory()->create([
        'birth_cert_number' => $birth_cert_number,
    ]);

    $nric = fake()->uuid();
    $tenth_enrollment = Enrollment::factory()->create([
        'nric' => $nric,
    ]);

    $gender = Gender::FEMALE->value;
    $eleventh_enrollment = Enrollment::factory()->create([
        'gender' => $gender,
    ]);

    $thirteen_enrollment = Enrollment::factory()->create([
        'admission_year' => 2035,
    ]);

    $passport_number = fake()->uuid();
    $fourteen_enrollment = Enrollment::factory()->create([
        'passport_number' => $passport_number,
    ]);

    $state = State::factory()->create();
    $fifteen_enrollment = Enrollment::factory()->create([
        'state_id' => $state->id,
    ]);

    $country = Country::factory()->create();
    $sixteen_enrollment = Enrollment::factory()->create([
        'country_id' => $country->id,
    ]);

    $seventeenth_enrollment = Enrollment::factory()->create([
        'enrollment_status' => EnrollmentStatus::REJECTED->value,
    ]);

    $eighteenth_enrollment = Enrollment::factory()->create([
        'payment_status' => EnrollmentPaymentStatus::PENDING->value,
    ]);

    $guardian_1 = EnrollmentGuardian::factory()->create([
        'name->en' => 'Mother AAA',
        'phone_number' => '*********',
        'enrollment_id' => $eighteenth_enrollment->id,
    ]);

    $enrollment_user = User::factory()->create();
    $nineteenth_enrollment = Enrollment::factory()->create([
        'enrollment_user_id' => $enrollment_user->id,
    ]);

    $guardian_2 = EnrollmentGuardian::factory()->create([
        'name->en' => 'Father CCC',
        'phone_number' => '11111111111',
        'enrollment_id' => $nineteenth_enrollment->id,
    ]);

    // =================================================================================

    // Filter by admission_year = 2035
    $response = $this->enrollmentRepository->getAllPaginated(['admission_year' => 2035])->toArray();

    expect($response['data'])->toHaveCount(1)->toEqual([$thirteen_enrollment->toArray()]);

    // =================================================================================

    // Filter by admission_grade_id = $grade->id
    $response = $this->enrollmentRepository->getAllPaginated(['admission_grade_id' => $grade->id])->toArray();

    expect($response['data'])->toHaveCount(1)->toEqual([$fourth_enrollment->toArray()]);

    // =================================================================================

    // Filter by name = John Jones
    $response = $this->enrollmentRepository->getAllPaginated(['name' => 'John Jones'])->toArray();

    expect($response['data'])->toHaveCount(1)->toEqual([$first_enrollment->toArray()]);

    // Filter by name = BBO Name
    $response = $this->enrollmentRepository->getAllPaginated(['name' => 'BBO Name'])->toArray();

    expect($response['data'])->toHaveCount(1)->toEqual([$third_enrollment->toArray()]);

    // Filter by non-existing name = Meh
    $response = $this->enrollmentRepository->getAllPaginated(['name' => 'Meh'])->toArray();

    expect($response['data'])->toHaveCount(0)->toBeEmpty();

    // =================================================================================

    // Filter by nric = $nric
    $response = $this->enrollmentRepository->getAllPaginated(['nric' => $nric])->toArray();

    expect($response['data'])->toHaveCount(1)->toEqual([$tenth_enrollment->toArray()]);

    // =================================================================================

    // Filter by nric = $nric (array)
    $response = $this->enrollmentRepository->getAllPaginated([
        'nric' => [
            $first_enrollment->nric,
            $tenth_enrollment->nric,
        ]
    ])->toArray();

    expect($response['data'])->toHaveCount(2)->toEqual([
        $first_enrollment->toArray(),
        $tenth_enrollment->toArray(),
    ]);

    // =================================================================================

    // Filter by passport_number = $passport_number
    $response = $this->enrollmentRepository->getAllPaginated(['passport_number' => $passport_number])->toArray();

    expect($response['data'])->toHaveCount(1)->toEqual([$fourteen_enrollment->toArray()]);

    // =================================================================================

    // Filter by passport_number = $passport_number (array)
    $response = $this->enrollmentRepository->getAllPaginated([
        'passport_number' => [
            $first_enrollment->passport_number,
            $second_enrollment->passport_number,
        ]
    ])->toArray();

    expect($response['data'])->toHaveCount(2)->toEqual([
        $first_enrollment->toArray(),
        $second_enrollment->toArray(),
    ]);

    // =================================================================================

    // Filter by nationality_id = $nationality->id
    $response = $this->enrollmentRepository->getAllPaginated(['nationality_id' => $nationality->id])->toArray();

    expect($response['data'])->toHaveCount(1)->toEqual([$sixth_enrollment->toArray()]);

    // =================================================================================

    // Filter by gender = $gender
    $response = $this->enrollmentRepository->getAllPaginated(['gender' => $gender])->toArray();

    expect($response['data'])->toHaveCount(1)->toEqual([$eleventh_enrollment->toArray()]);

    // =================================================================================


    // Filter by birth_cert_number = $birth_cert_number
    $response = $this->enrollmentRepository->getAllPaginated(['birth_cert_number' => $birth_cert_number])->toArray();

    expect($response['data'])->toHaveCount(1)->toEqual([$nineth_enrollment->toArray()]);

    // =================================================================================

    // Filter by race_id = $race->id
    $response = $this->enrollmentRepository->getAllPaginated(['race_id' => $race->id])->toArray();

    expect($response['data'])->toHaveCount(1)->toEqual([$seventh_enrollment->toArray()]);

    // =================================================================================

    // Filter by religion_id = $religion->id
    $response = $this->enrollmentRepository->getAllPaginated(['religion_id' => $religion->id])->toArray();

    expect($response['data'])->toHaveCount(1)->toEqual([$eighth_enrollment->toArray()]);

    // =================================================================================

    // Filter by state_id = $state->id
    $response = $this->enrollmentRepository->getAllPaginated(['state_id' => $state->id])->toArray();

    expect($response['data'])->toHaveCount(1)->toEqual([$fifteen_enrollment->toArray()]);

    // =================================================================================

    // Filter by country_id = $country->id
    $response = $this->enrollmentRepository->getAllPaginated(['country_id' => $country->id])->toArray();

    expect($response['data'])->toHaveCount(1)->toEqual([$sixteen_enrollment->toArray()]);

    // =================================================================================

    // Filter by enrollment_status = REJECTED
    $response = $this->enrollmentRepository->getAllPaginated(['enrollment_status' => EnrollmentStatus::REJECTED->value])->toArray();

    expect($response['data'])->toHaveCount(1)->toEqual([$seventeenth_enrollment->toArray()]);

    // =================================================================================

    // Filter by payment_status = PENDING
    $response = $this->enrollmentRepository->getAllPaginated(['payment_status' => EnrollmentPaymentStatus::PENDING->value])->toArray();

    expect($response['data'])->toHaveCount(1)->toEqual([$eighteenth_enrollment->toArray()]);

    // =================================================================================

    // Filter by enrollment_user_id = $enrollment_user->id
    $response = $this->enrollmentRepository->getAllPaginated(['enrollment_user_id' => $enrollment_user->id])->toArray();

    expect($response['data'])->toHaveCount(1)->toEqual([$nineteenth_enrollment->toArray()]);

    // =================================================================================

    // Filter by enrollment_session_id = $second_enrollment->enrollment_session_id
    $response = $this->enrollmentRepository->getAllPaginated(['enrollment_session_id' => $second_enrollment->enrollment_session_id])->toArray();

    expect($response['data'])->toHaveCount(1)->toEqual([$second_enrollment->toArray()]);

    // =================================================================================

    // Filter by guardian_name = 'Mother AAA
    $response = $this->enrollmentRepository->getAllPaginated(['guardian_name' => 'Mother AAA'])->toArray();

    expect($response['data'])->toHaveCount(1)->toEqual([$eighteenth_enrollment->toArray()]);

    // =================================================================================

    // Filter by guardian_name = 'FatherDCCC'

    $response = $this->enrollmentRepository->getAllPaginated(['guardian_name' => 'FatherDCCC'])->toArray();
    
    expect($response['data'])->toHaveCount(0)->toEqual([]);

    // =================================================================================

    // Filter by guardian_phone_number = '*********'
    
    $response = $this->enrollmentRepository->getAllPaginated(['guardian_phone_number' => '*********'])->toArray();
    
    expect($response['data'])->toHaveCount(1)->toEqual([$eighteenth_enrollment->toArray()]);

    // =================================================================================

    // Filter by guardian_phone_number = '11111111111ss'

    $response = $this->enrollmentRepository->getAllPaginated(['guardian_phone_number' => '11111111111ss'])->toArray();

    expect($response['data'])->toHaveCount(0)->toEqual([]);

    // =================================================================================
    // =================================================================================
    // =================================================================================

    // Sort by id asc
    $response = $this->enrollmentRepository->getAllPaginated([
        'order_by' => ['id' => 'asc'],
    ])->toArray();

    expect($response['data'])->sequence(
        fn($enrollment) => $enrollment->id->toBe($first_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($second_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($third_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($fourth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($fifth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($sixth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($seventh_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($eighth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($nineth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($tenth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($eleventh_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($thirteen_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($fourteen_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($fifteen_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($sixteen_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($seventeenth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($eighteenth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($nineteenth_enrollment->id),
    );

    // Sort by id desc
    $response = $this->enrollmentRepository->getAllPaginated([
        'order_by' => ['id' => 'desc'],
    ])->toArray();

    expect($response['data'])->sequence(
        fn($enrollment) => $enrollment->id->toBe($nineteenth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($eighteenth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($seventeenth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($sixteen_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($fifteen_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($fourteen_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($thirteen_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($eleventh_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($tenth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($nineth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($eighth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($seventh_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($sixth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($fifth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($fourth_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($third_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($second_enrollment->id),
        fn($enrollment) => $enrollment->id->toBe($first_enrollment->id),
    );

    // Sort by nationality_id asc
    $response = $this->enrollmentRepository->getAllPaginated([
        'order_by' => ['nationality_id' => 'asc'],
    ])->toArray();

    expect($response['data'])->sequence(
        fn($enrollment) => $enrollment->nationality_id->toBe($first_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($second_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($third_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($fourth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($fifth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($sixth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($seventh_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($eighth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($nineth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($tenth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($eleventh_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($thirteen_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($fourteen_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($fifteen_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($sixteen_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($seventeenth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($eighteenth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($nineteenth_enrollment->nationality_id),
    );

    // Sort by nationality_id desc
    $response = $this->enrollmentRepository->getAllPaginated([
        'order_by' => ['nationality_id' => 'desc'],
    ])->toArray();

    expect($response['data'])->sequence(
        fn($enrollment) => $enrollment->nationality_id->toBe($nineteenth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($eighteenth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($seventeenth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($sixteen_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($fifteen_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($fourteen_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($thirteen_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($eleventh_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($tenth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($nineth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($eighth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($seventh_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($sixth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($fifth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($fourth_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($third_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($second_enrollment->nationality_id),
        fn($enrollment) => $enrollment->nationality_id->toBe($first_enrollment->nationality_id),
    );
});
