<?php

use App\Enums\ClassStream;
use App\Enums\ClassType;
use App\Enums\EnglishLevel;
use App\Enums\Gender;
use App\Jobs\RefreshMaterializedViewJob;
use App\Models\ClassModel;
use App\Models\ClassSubject;
use App\Models\Course;
use App\Models\Employee;
use App\Models\Grade;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\SemesterYearSetting;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\Subject;
use App\Services\ClassService;
use Database\Seeders\InternationalizationSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;

beforeEach(function () {
    $this->seed(InternationalizationSeeder::class);

    $this->classService = app(ClassService::class);

    $this->class_table_name = resolve(ClassModel::class)->getTable();

    app()->setLocale('en');

    $this->test_locale = app()->getLocale();
});

test('getAllPaginatedClasses()', function () {
    $course_uec = Course::factory()->create();
    $course_igcse = Course::factory()->igcse()->create();

    $semester_year_setting_2024 = SemesterYearSetting::firstOrCreate(['year' => 2024]);
    $semester_year_setting_2025 = SemesterYearSetting::firstOrCreate(['year' => 2025]);

    $first_homeroom_teacher = Employee::factory()->create();

    $semester_setting_uec_sem_1 = SemesterSetting::factory()->create([
        'course_id' => $course_uec->id,
        'semester_year_setting_id' => $semester_year_setting_2024->id,
        'name' => '2024 Semester 1'
    ]);

    $semester_setting_uec_sem_2 = SemesterSetting::factory()->create([
        'course_id' => $course_uec->id,
        'semester_year_setting_id' => $semester_year_setting_2024->id,
        'name' => '2024 Semester 2'
    ]);

    $semester_setting_igcse_sem_1 = SemesterSetting::factory()->create([
        'course_id' => $course_igcse->id,
        'semester_year_setting_id' => $semester_year_setting_2025->id,
        'name' => '2025 Semester 1'
    ]);

    $semester_setting_igcse_sem_2 = SemesterSetting::factory()->create([
        'course_id' => $course_igcse->id,
        'semester_year_setting_id' => $semester_year_setting_2025->id,
        'name' => '2025 Semester 2'
    ]);

    $first_grade_uec = Grade::factory()->create(['name' => 'Junior 1']);
    $second_grade_uec = Grade::factory()->create(['name' => 'Junior 2']);

    $first_grade_igcse = Grade::factory()->create(['name' => 'Year 1']);
    $second_grade_igcse = Grade::factory()->create(['name' => 'Year 2']);

    $first_class_uec = ClassModel::factory()->create([
        'name->en' => 'J111',
        'name->zh' => '初一11班',
        'grade_id' => $first_grade_uec->id,
        'type' => ClassType::PRIMARY,
    ]);

    $first_class_igcse = ClassModel::factory()->create([
        'name->en' => 'Y111',
        'grade_id' => $first_grade_igcse->id,
        'type' => ClassType::PRIMARY
    ]);

    $first_class_english = ClassModel::factory()->create([
        'name->en' => 'EA',
        'type' => ClassType::ENGLISH,
        'grade_id' => null
    ]);

    $first_class_club = ClassModel::factory()->create([
        'name->en' => 'Group A',
        'type' => ClassType::SOCIETY,
        'grade_id' => null
    ]);


    $result = $this->classService->getAllPaginatedClasses()->toArray();
    expect($result['data'])->toHaveCount(4)->sequence(
        fn($item) => $item->toEqual($first_class_uec->load(['semesterClasses', 'grade'])->toArray()),
        fn($item) => $item->toEqual($first_class_igcse->load(['semesterClasses', 'grade'])->toArray()),
        fn($item) => $item->toEqual($first_class_english->load(['semesterClasses', 'grade'])->toArray()),
        fn($item) => $item->toEqual($first_class_club->load(['semesterClasses', 'grade'])->toArray()),
    );

    // Random test
    $result = $this->classService->getAllPaginatedClasses(['name' => ['en' => 'J111']])->toArray();
    expect($result['data'])->toHaveCount(1)
        ->toEqual([$first_class_uec->toArray()]);
});

test('getAllClasses()', function () {
    $course_uec = Course::factory()->create();
    $course_igcse = Course::factory()->igcse()->create();

    $semester_year_setting_2024 = SemesterYearSetting::firstOrCreate(['year' => 2024]);
    $semester_year_setting_2025 = SemesterYearSetting::firstOrCreate(['year' => 2025]);

    $first_homeroom_teacher = Employee::factory()->create();

    $semester_setting_uec_sem_1 = SemesterSetting::factory()->create([
        'course_id' => $course_uec->id,
        'semester_year_setting_id' => $semester_year_setting_2024->id,
        'name' => '2024 Semester 1'
    ]);

    $semester_setting_uec_sem_2 = SemesterSetting::factory()->create([
        'course_id' => $course_uec->id,
        'semester_year_setting_id' => $semester_year_setting_2024->id,
        'name' => '2024 Semester 2'
    ]);

    $semester_setting_igcse_sem_1 = SemesterSetting::factory()->create([
        'course_id' => $course_igcse->id,
        'semester_year_setting_id' => $semester_year_setting_2025->id,
        'name' => '2025 Semester 1'
    ]);

    $semester_setting_igcse_sem_2 = SemesterSetting::factory()->create([
        'course_id' => $course_igcse->id,
        'semester_year_setting_id' => $semester_year_setting_2025->id,
        'name' => '2025 Semester 2'
    ]);

    $first_grade_uec = Grade::factory()->create(['name' => 'Junior 1']);
    $second_grade_uec = Grade::factory()->create(['name' => 'Junior 2']);

    $first_grade_igcse = Grade::factory()->create(['name' => 'Year 1']);
    $second_grade_igcse = Grade::factory()->create(['name' => 'Year 2']);

    $first_class_uec = ClassModel::factory()->create([
        'name->en' => 'J111',
        'name->zh' => '初一11班',
        'grade_id' => $first_grade_uec->id,
        'type' => ClassType::PRIMARY,
    ]);

    $first_class_igcse = ClassModel::factory()->create([
        'name->en' => 'Y111',
        'grade_id' => $first_grade_igcse->id,
        'type' => ClassType::PRIMARY
    ]);

    $first_class_english = ClassModel::factory()->create([
        'name->en' => 'EA',
        'type' => ClassType::ENGLISH,
        'grade_id' => null
    ]);

    $first_class_club = ClassModel::factory()->create([
        'name->en' => 'Group A',
        'type' => ClassType::SOCIETY,
        'grade_id' => null
    ]);


    $result = $this->classService->getAllClasses()->toArray();
    expect($result)->toHaveCount(4)->sequence(
        fn($item) => $item->toEqual($first_class_uec->load(['semesterClasses', 'grade'])->toArray()),
        fn($item) => $item->toEqual($first_class_igcse->load(['semesterClasses', 'grade'])->toArray()),
        fn($item) => $item->toEqual($first_class_english->load(['semesterClasses', 'grade'])->toArray()),
        fn($item) => $item->toEqual($first_class_club->load(['semesterClasses', 'grade'])->toArray()),
    );

    // Random test
    $result = $this->classService->getAllClasses(['name' => ['en' => 'J111']])->toArray();
    expect($result)->toHaveCount(1)
        ->toEqual([$first_class_uec->toArray()]);
});

test('createClass()', function () {
    //store success
    $this->assertDatabaseCount($this->class_table_name, 0);

    $grade = Grade::factory()->create();

    $employee = Employee::factory()->create();

    $payload = [
        'name' => [
            'en' => 'J111',
            'zh' => '初一11班',
        ],
        'type' => 'PRIMARY',
        'code' => 'J111',
        'stream' => ClassStream::COMMERCE->value,
        'grade_id' => $grade->id,
        'is_active' => true,
    ];

    $response = $this->classService->createClass($payload)->toArray();

    expect($response)->toMatchArray($payload);

    $this->assertDatabaseCount($this->class_table_name, 1);

    $this->assertDatabaseHas($this->class_table_name, [
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
        'stream' => $payload['stream'],
        'code' => $payload['code'],
        'type' => $payload['type'],
        'grade_id' => $payload['grade_id'],
        'is_active' => $payload['is_active'],
    ]);

    // create english class
    $payload = [
        'name' => [
            'en' => 'J211',
            'zh' => '初一11班',
        ],
        'code' => 'J120',
        'stream' => ClassStream::COMMERCE->value,
        'type' => ClassType::ENGLISH->value,
        'english_level' => EnglishLevel::ADVANCED_1->value,
        'grade_id' => $grade->id,
        'is_active' => true,
    ];

    $response = $this->classService->createClass($payload)->toArray();

    expect($response)->toMatchArray($payload);

    $this->assertDatabaseCount($this->class_table_name, 2);

    $this->assertDatabaseHas($this->class_table_name, [
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
        'code' => $payload['code'],
        'type' => $payload['type'],
        'stream' => $payload['stream'],
        'english_level' => $payload['english_level'],
        'grade_id' => $payload['grade_id'],
        'is_active' => $payload['is_active'],
    ]);
});

test('updateClass()', function () {
    $first = ClassModel::factory()->create();

    //update with id exist
    $this->assertDatabaseCount($this->class_table_name, 1);

    $grade = Grade::factory()->create();

    $employee = Employee::factory()->create();

    $payload = [
        'name' => [
            'en' => 'Updated 1',
            'zh' => '初一',
        ],
        'code' => 'UPDATE1',
        'type' => 'PRIMARY',
        'stream' => ClassStream::COMMERCE->value,
        'grade_id' => $grade->id,
        'is_active' => false,
    ];

    $response = $this->classService->updateClass($first, $payload)->toArray();

    expect($response)->toMatchArray($payload);

    $this->assertDatabaseCount($this->class_table_name, 1);

    $this->assertDatabaseHas($this->class_table_name, [
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
        'code' => $payload['code'],
        'type' => $payload['type'],
        'stream' => $payload['stream'],
        'grade_id' => $payload['grade_id'],
        'is_active' => $payload['is_active'],
    ]);


    // update to english class
    $payload = [
        'name' => [
            'en' => 'Updated 1',
            'zh' => '初一',
        ],
        'code' => 'UPDATE1',
        'stream' => ClassStream::COMMERCE->value,
        'type' => ClassType::ENGLISH->value,
        'english_level' => EnglishLevel::ADVANCED_1->value,
        'grade_id' => $grade->id,
        'is_active' => false,
    ];

    $response = $this->classService->updateClass($first, $payload)->toArray();

    expect($response)->toMatchArray($payload);

    $this->assertDatabaseCount($this->class_table_name, 1);

    $this->assertDatabaseHas($this->class_table_name, [
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
        'type' => $payload['type'],
        'stream' => $payload['stream'],
        'english_level' => $payload['english_level'],
        'grade_id' => $payload['grade_id'],
        'is_active' => $payload['is_active'],
    ]);
});

test('assignClassToSemester()', function () {
    $course = Course::factory()->create();

    $semester_setting_year_2024 = SemesterYearSetting::create(['year' => 2024]);

    $semester_setting_one = SemesterSetting::factory()->create([
        'course_id' => $course->id,
        'name' => '2024 Semester 1',
        'semester_year_setting_id' => $semester_setting_year_2024->id,
        'is_current_semester' => true
    ]);

    $semester_setting_two = SemesterSetting::factory()->create([
        'course_id' => $course->id,
        'name' => '2024 Semester 2',
        'semester_year_setting_id' => $semester_setting_year_2024->id,
        'is_current_semester' => true
    ]);

    $first_grade_uec = Grade::factory()->create(['name' => 'Junior 1']);
    $second_grade_uec = Grade::factory()->create(['name' => 'Junior 2']);

    $first_homeroom_teacher = Employee::factory()->create();
    $second_homeroom_teacher = Employee::factory()->create();

    $first_class_uec = ClassModel::factory()->create([
        'name->en' => 'J111',
        'name->zh' => '初一11班',
        'grade_id' => $first_grade_uec->id,
        'type' => ClassType::PRIMARY,
    ]);

    $second_class_uec = ClassModel::factory()->create([
        'name->en' => 'J211',
        'name->zh' => '初二11班',
        'grade_id' => $second_grade_uec->id,
        'type' => ClassType::PRIMARY,
    ]);

    $payload = [
        'semester_setting_id' => $semester_setting_one->id,
        'classes' => [
            [
                'id' => $first_class_uec->id,
                'homeroom_teacher_id' => $first_homeroom_teacher->id,
                'is_active' => true,
            ],
            [
                'id' => $second_class_uec->id,
                'homeroom_teacher_id' => $first_homeroom_teacher->id,
                'is_active' => true
            ]
        ]
    ];

    $this->assertDatabaseCount('semester_classes', 0);
    $this->classService->assignClassToSemester($payload);

    $this->assertDatabaseCount('semester_classes', 2);
    $this->assertDatabaseHas('semester_classes', [
        'semester_setting_id' => $semester_setting_one->id,
        'class_id' => $first_class_uec->id,
        'homeroom_teacher_id' => $first_homeroom_teacher->id,
        'is_active' => true
    ]);

    $this->assertDatabaseHas('semester_classes', [
        'semester_setting_id' => $semester_setting_one->id,
        'class_id' => $second_class_uec->id,
        'homeroom_teacher_id' => $first_homeroom_teacher->id,
        'is_active' => true
    ]);

    // Change is_active status to false
    $payload = [
        'semester_setting_id' => $semester_setting_one->id,
        'classes' => [
            [
                'id' => $first_class_uec->id,
                'homeroom_teacher_id' => $second_homeroom_teacher->id,
                'is_active' => false,
            ],
            [
                'id' => $second_class_uec->id,
                'homeroom_teacher_id' => $second_homeroom_teacher->id,
                'is_active' => false
            ]
        ]
    ];

    $this->classService->assignClassToSemester($payload);

    $this->assertDatabaseCount('semester_classes', 2);
    $this->assertDatabaseHas('semester_classes', [
        'semester_setting_id' => $semester_setting_one->id,
        'class_id' => $first_class_uec->id,
        'homeroom_teacher_id' => $second_homeroom_teacher->id,
        'is_active' => false
    ]);

    $this->assertDatabaseHas('semester_classes', [
        'semester_setting_id' => $semester_setting_one->id,
        'class_id' => $second_class_uec->id,
        'homeroom_teacher_id' => $second_homeroom_teacher->id,
        'is_active' => false
    ]);

    // Assign to second semester
    $payload = [
        'semester_setting_id' => $semester_setting_two->id,
        'classes' => [
            [
                'id' => $first_class_uec->id,
                'homeroom_teacher_id' => $second_homeroom_teacher->id,
                'is_active' => true,
            ],
            [
                'id' => $second_class_uec->id,
                'homeroom_teacher_id' => $second_homeroom_teacher->id,
                'is_active' => true
            ]
        ]
    ];

    $this->classService->assignClassToSemester($payload);

    $this->assertDatabaseCount('semester_classes', 4);
    $this->assertDatabaseHas('semester_classes', [
        'semester_setting_id' => $semester_setting_two->id,
        'class_id' => $first_class_uec->id,
        'homeroom_teacher_id' => $second_homeroom_teacher->id,
        'is_active' => true
    ]);

    $this->assertDatabaseHas('semester_classes', [
        'semester_setting_id' => $semester_setting_two->id,
        'class_id' => $second_class_uec->id,
        'homeroom_teacher_id' => $second_homeroom_teacher->id,
        'is_active' => true
    ]);
});

test('assignClassToStudent(), assign to students with no class', function () {
    $course = Course::factory()->create();

    $semester_setting_year_2024 = SemesterYearSetting::create(['year' => 2024]);

    $semester_setting_one = SemesterSetting::factory()->create([
        'course_id' => $course->id,
        'name' => '2024 Semester 1',
        'semester_year_setting_id' => $semester_setting_year_2024->id,
        'is_current_semester' => true
    ]);

    $semester_setting_two = SemesterSetting::factory()->create([
        'course_id' => $course->id,
        'name' => '2024 Semester 2',
        'semester_year_setting_id' => $semester_setting_year_2024->id,
        'is_current_semester' => true
    ]);

    $first_grade_uec = Grade::factory()->create(['name' => 'Junior 1']);
    $second_grade_uec = Grade::factory()->create(['name' => 'Junior 2']);

    $homeroom_teacher = Employee::factory()->create();

    $first_class_uec = ClassModel::factory()->create([
        'name->en' => 'J111',
        'name->zh' => '初一11班',
        'grade_id' => $first_grade_uec->id,
        'type' => ClassType::PRIMARY,
    ]);

    $second_class_uec = ClassModel::factory()->create([
        'name->en' => 'J211',
        'name->zh' => '初二11班',
        'grade_id' => $second_grade_uec->id,
        'type' => ClassType::PRIMARY,
    ]);

    $first_class_uec_2024_sem_1 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting_one->id,
        'class_id' => $first_class_uec->id,
        'homeroom_teacher_id' => $homeroom_teacher->id,
        'is_active' => true
    ]);

    $first_student = Student::factory()->create();
    $second_student = Student::factory()->create();

    $this->assertDatabaseCount('student_classes', 0);
    $payload = [
        'semester_setting_id' => $semester_setting_one->id,
        'semester_class_id' => $first_class_uec_2024_sem_1->id,
        'students' => [
            [
                'id' => $first_student->id,
                'seat_no' => 1,
                'is_active' => true,
                'class_enter_date' => '2024-08-08'
            ],
            [
                'id' => $second_student->id,
                'seat_no' => 2,
                'is_active' => true,
                'class_enter_date' => '2024-08-07'
            ]
        ]
    ];

    Queue::fake();
    $this->classService->assignClassToStudent($payload);

    Queue::assertPushedOn('materialized-views', RefreshMaterializedViewJob::class);
    Queue::assertPushed(RefreshMaterializedViewJob::class, 4);

    $this->assertDatabaseCount('student_classes', 2);
    $this->assertDatabaseHas('student_classes', [
        'semester_setting_id' => $semester_setting_one->id,
        'semester_class_id' => $first_class_uec_2024_sem_1->id,
        'class_type' => 'PRIMARY',
        'student_id' => $first_student->id,
        'class_enter_date' => '2024-08-08',
        'is_active' => true,
        'is_latest_class_in_semester' => true
    ]);

    $this->assertDatabaseHas('student_classes', [
        'semester_setting_id' => $semester_setting_one->id,
        'semester_class_id' => $first_class_uec_2024_sem_1->id,
        'class_type' => 'PRIMARY',
        'student_id' => $second_student->id,
        'class_enter_date' => '2024-08-07',
        'is_active' => true,
        'is_latest_class_in_semester' => true
    ]);

    expect($first_class_uec_2024_sem_1->activeStudentClasses)->toHaveCount(2);
});

test('assignClassToStudent() : new assignment with link to class_subject', function () {
    $semester_setting = SemesterSetting::factory()->create();
    $class = ClassModel::factory()->create();
    $homeroom_teacher = Employee::factory()->create();

    $semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $class->id,
        'homeroom_teacher_id' => $homeroom_teacher->id,
        'is_active' => true,
    ]);

    $student_1 = Student::factory()->create();
    $student_2 = Student::factory()->create();

    $subject_1 = Subject::factory()->create();
    $subject_2 = Subject::factory()->create();

    // Link 2 subject to $semester_class
    $class_subject_1 = ClassSubject::create(['semester_class_id' => $semester_class->id, 'subject_id' => $subject_1->id]);
    $class_subject_2 = ClassSubject::create(['semester_class_id' => $semester_class->id, 'subject_id' => $subject_2->id]);

    $this->assertDatabaseCount('student_classes', 0);
    $this->assertDatabaseCount('class_subject_student', 0);

    $payload = [
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class->id,
        'class_subject_ids' => [$class_subject_1->id, $class_subject_2->id], // populate students under $semester_class into this class_subject
        'students' => [
            [
                'id' => $student_1->id,
                'class_enter_date' => '2024-07-01',
                'is_active' => true,
            ],
            [
                'id' => $student_2->id,
                'class_enter_date' => '2024-07-01',
                'is_active' => true,
            ]
        ]
    ];

    $this->classService->assignClassToStudent($payload);

    $this->assertDatabaseCount('student_classes', 2);

    foreach ($payload['students'] as $student) {
        $this->assertDatabaseHas('student_classes', [
            'semester_setting_id' => $payload['semester_setting_id'],
            'semester_class_id' => $payload['semester_class_id'],
            'student_id' => $student['id'],
            'class_enter_date' => $student['class_enter_date'],
            'is_active' => $student['is_active'],
            'is_latest_class_in_semester' => true
        ]);
    }

    $this->assertDatabaseCount('class_subject_student', 4);

    // assert both student is also under each $class_subject
    foreach ($payload['students'] as $student) {
        $this->assertDatabaseHas('class_subject_student', [
            'class_subject_id' => $class_subject_1->id,
            'student_id' => $student['id'],
        ]);

        $this->assertDatabaseHas('class_subject_student', [
            'class_subject_id' => $class_subject_2->id,
            'student_id' => $student['id'],
        ]);
    }
});

test('assignClassToStudent() : student existing in a class with subject is moved to another class with subject', function () {
    $sem_year = SemesterYearSetting::create([
        'year' => 2025
    ]);

    $sem1 = SemesterSetting::factory()->create([
        'name' => '2025 Semester 1',
        'semester_year_setting_id' => $sem_year->id,
    ]);

    $grades = Grade::factory(2)->create(new Sequence(
        [
            'name->en' => 'Junior 1'
        ],
    ));

    $classes = ClassModel::factory(2)->create(new Sequence(
        [
            'name->en' => 'J1001',
            'type' => ClassType::PRIMARY,
            'grade_id' => $grades[0]->id
        ],
        [
            'name->en' => 'J1002',
            'type' => ClassType::PRIMARY,
            'grade_id' => $grades[0]->id
        ],
    ));

    $semester_classes = SemesterClass::factory(2)->create(new Sequence(
        [
            'semester_setting_id' => $sem1->id,
            'class_id' => $classes[0]->id // J1001
        ],
        [
            'semester_setting_id' => $sem1->id,
            'class_id' => $classes[1]->id // J1002
        ],
    ));

    $students = Student::factory(2)->create(new Sequence(
        [
            'name->en' => 'John Jones',
            'gender' => Gender::MALE->value,
        ],
        [
            'name->en' => 'Dwayne',
            'gender' => Gender::MALE->value,
        ],
    ));

    $student_classes = StudentClass::factory(2)->create(new Sequence(
        [
            'semester_setting_id' => $sem1->id,
            'semester_class_id' => $semester_classes[0]->id,
            'class_type' => $semester_classes[0]->classModel->type->value,
            'student_id' => $students[0]->id,
            'class_enter_date' => '2024-07-01',
            'is_active' => true,
        ],
        [
            'semester_setting_id' => $sem1->id,
            'semester_class_id' => $semester_classes[0]->id,
            'class_type' => $semester_classes[0]->classModel->type->value,
            'student_id' => $students[1]->id,
            'class_enter_date' => '2024-07-01',
            'is_active' => true,
        ],
    ));

    $subjects = Subject::factory(2)->create(new Sequence(
        [
            'type' => 'MAJOR',
            'code' => 'ENG-100',
            'name->en' => 'English',
        ],
        [
            'type' => 'MAJOR',
            'code' => 'HIST-100',
            'name->en' => 'History',
        ],
    ));

    // Link 1 subject to $semester_classes[0]
    $english_subject = ClassSubject::create(['semester_class_id' => $semester_classes[0]->id, 'subject_id' => $subjects[0]->id]);

    // Link 2 students to $english_subject
    $english_subject->students()->sync($students->pluck('id'));


    // Link 1 subject to $semester_classes[1]
    $history_subject = ClassSubject::create(['semester_class_id' => $semester_classes[1]->id, 'subject_id' => $subjects[1]->id]);

    expect($semester_classes[0]->refresh()->activeStudentClasses)->toHaveCount(2);
    expect($semester_classes[1]->refresh()->activeStudentClasses)->toHaveCount(0);

    expect($english_subject->refresh()->students)->toHaveCount(2);
    expect($history_subject->refresh()->students)->toHaveCount(0);

    /**
     *
     * assign $students[0] John to another PRIMARY semester_class in the same semester
     * assign $students[0] John to $history_subject
     *
     */
    $payload = [
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[1]->id,
        'class_subject_ids' => [$history_subject->id],
        'students' => [
            [
                'id' => $students[0]->id,  // John
                'class_enter_date' => '2024-07-02',
                'is_active' => true,
            ],
        ]
    ];

    $this->classService->assignClassToStudent($payload);

    $this->assertDatabaseHas('student_classes', [
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id,
        'student_id' => $students[1]->id, // Dwayne is in original class
        'class_enter_date' => '2024-07-01',
        'is_active' => true,
        'is_latest_class_in_semester' => true
    ]);

    $this->assertDatabaseMissing('student_classes', [
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id,
        'student_id' => $students[1]->id, // John old record is now false
        'class_enter_date' => '2024-07-01',
        'is_active' => false,
        'is_latest_class_in_semester' => false
    ]);

    $this->assertDatabaseMissing('student_classes', [
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[1]->id,
        'student_id' => $students[1]->id, // John is in new class
        'class_enter_date' => '2024-07-02',
        'is_active' => true,
        'is_latest_class_in_semester' => true
    ]);

    expect($semester_classes[0]->refresh()->activeStudentClasses)->toHaveCount(1);
    expect($semester_classes[1]->refresh()->activeStudentClasses)->toHaveCount(1);

    expect($english_subject->refresh()->students)->toHaveCount(2); // 2 because it should be removed from the API `assign-subjects-to-semester-classes`
    expect($history_subject->refresh()->students)->toHaveCount(1);

    $this->assertDatabaseHas('class_subject_student', [
        'class_subject_id' => $english_subject->id,
        'student_id' => $students[1]->id, // Dwayne is in english subject
    ]);

    $this->assertDatabaseHas('class_subject_student', [
        'class_subject_id' => $english_subject->id,
        'student_id' => $students[0]->id, // John is not in english subject
    ]);

    $this->assertDatabaseHas('class_subject_student', [
        'class_subject_id' => $history_subject->id,
        'student_id' => $students[0]->id, // John is in history subject
    ]);

    $this->assertDatabaseMissing('class_subject_student', [
        'class_subject_id' => $history_subject->id,
        'student_id' => $students[1]->id, // Dwayne is not in history subject
    ]);
});

test('assignClassToStudent() : student in class with subject is removed from class and subject', function () {
    $sem_year = SemesterYearSetting::create([
        'year' => 2025
    ]);

    $sem1 = SemesterSetting::factory()->create([
        'name' => '2025 Semester 1',
        'semester_year_setting_id' => $sem_year->id,
    ]);

    $grades = Grade::factory(2)->create(new Sequence(
        [
            'name->en' => 'Junior 1'
        ],
    ));

    $classes = ClassModel::factory(2)->create(new Sequence(
        [
            'name->en' => 'J1001',
            'type' => ClassType::PRIMARY,
            'grade_id' => $grades[0]->id
        ],
        [
            'name->en' => 'J1002',
            'type' => ClassType::PRIMARY,
            'grade_id' => $grades[0]->id
        ],
    ));

    $semester_classes = SemesterClass::factory(2)->create(new Sequence(
        [
            'semester_setting_id' => $sem1->id,
            'class_id' => $classes[0]->id // J1001
        ],
        [
            'semester_setting_id' => $sem1->id,
            'class_id' => $classes[1]->id // J1002
        ],
    ));

    $students = Student::factory(2)->create(new Sequence(
        [
            'name->en' => 'John Jones',
            'gender' => Gender::MALE->value,
        ],
        [
            'name->en' => 'Dwayne',
            'gender' => Gender::MALE->value,
        ],
    ));

    $student_classes = StudentClass::factory(2)->create(new Sequence(
        [
            'semester_setting_id' => $sem1->id,
            'semester_class_id' => $semester_classes[0]->id,
            'class_type' => $semester_classes[0]->classModel->type->value,
            'student_id' => $students[0]->id,
            'class_enter_date' => '2024-07-01',
            'is_active' => true,
        ],
        [
            'semester_setting_id' => $sem1->id,
            'semester_class_id' => $semester_classes[0]->id,
            'class_type' => $semester_classes[0]->classModel->type->value,
            'student_id' => $students[1]->id,
            'class_enter_date' => '2024-07-01',
            'is_active' => true,
        ],
    ));

    $subjects = Subject::factory(1)->create(new Sequence(
        [
            'type' => 'MAJOR',
            'code' => 'ENG-100',
            'name->en' => 'English',
        ],
    ));

    // Link 1 subject to $semester_classes[0]
    $english_subject = ClassSubject::create(['semester_class_id' => $semester_classes[0]->id, 'subject_id' => $subjects[0]->id]);

    // Link 2 students to $english_subject
    $english_subject->students()->sync($students->pluck('id'));


    expect($semester_classes[0]->refresh()->activeStudentClasses)->toHaveCount(2);
    expect($semester_classes[1]->refresh()->activeStudentClasses)->toHaveCount(0);

    expect($english_subject->refresh()->students)->toHaveCount(2);


    /**
     *
     * remove dwayne from english and $semester_classes[0]->id
     *
     */
    $payload = [
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id,
        'class_subject_ids' => [$english_subject->id],
        'students' => [
            [
                'id' => $students[0]->id,  // John
                'class_enter_date' => '2024-07-02',
                'is_active' => true,
            ],
        ]
    ];

    $this->classService->assignClassToStudent($payload);

    expect($semester_classes[0]->refresh()->activeStudentClasses)->toHaveCount(1);
    expect($semester_classes[1]->refresh()->activeStudentClasses)->toHaveCount(0);

    expect($english_subject->refresh()->students)->toHaveCount(1);

    $this->assertDatabaseHas('student_classes', [
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id,
        'student_id' => $students[0]->id, // John original is_active false
        'class_enter_date' => '2024-07-01',
        'is_active' => false,
        'is_latest_class_in_semester' => false
    ]);

    $this->assertDatabaseHas('student_classes', [
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id,
        'student_id' => $students[1]->id, // Dwayne original is_active false
        'class_enter_date' => '2024-07-01',
        'is_active' => false,
        'is_latest_class_in_semester' => false
    ]);

    $this->assertDatabaseHas('student_classes', [
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id,
        'student_id' => $students[0]->id, // John new record new date is_active true
        'class_enter_date' => '2024-07-02',
        'is_active' => true,
        'is_latest_class_in_semester' => true
    ]);

    $this->assertDatabaseHas('class_subject_student', [
        'class_subject_id' => $english_subject->id,
        'student_id' => $students[0]->id, // John is in english subject
    ]);

    $this->assertDatabaseMissing('class_subject_student', [
        'class_subject_id' => $english_subject->id,
        'student_id' => $students[1]->id, // Dwayne is not in english subject
    ]);
});

test('assignClassToStudent() : assign first, then remove 1 student, then remove all students', function () {
    $semester_setting = SemesterSetting::factory()->create();
    $class = ClassModel::factory()->create();
    $homeroom_teacher = Employee::factory()->create();

    $semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $class->id,
        'homeroom_teacher_id' => $homeroom_teacher->id,
        'is_active' => true,
    ]);

    $student_1 = Student::factory()->create();
    $student_2 = Student::factory()->create();

    $this->assertDatabaseCount('student_classes', 0);


    /** ASSIGN 2 STUDENTS TO THE SEMESTER_CLASS */
    $payload = [
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class->id,
        'class_subject_ids' => [], // empty
        'students' => [
            [
                'id' => $student_1->id,
                'class_enter_date' => '2024-07-01',
                'is_active' => true,
            ],
            [
                'id' => $student_2->id,
                'class_enter_date' => '2024-07-01',
                'is_active' => true,
            ]
        ]
    ];

    $this->classService->assignClassToStudent($payload);

    expect($semester_class->refresh()->activeStudentClasses)->toHaveCount(2);

    $this->assertDatabaseCount('student_classes', 2);

    foreach ($payload['students'] as $student) {
        $this->assertDatabaseHas('student_classes', [
            'semester_setting_id' => $payload['semester_setting_id'],
            'semester_class_id' => $payload['semester_class_id'],
            'student_id' => $student['id'],
            'class_enter_date' => $student['class_enter_date'],
            'is_active' => $student['is_active'],
            'is_latest_class_in_semester' => true
        ]);
    }


    /** REMOVE 1 STUDENT FROM THE SEMESTER_CLASS -> expect 1 student_class is_active = false */
    $payload = [
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class->id,
        'class_subject_ids' => [], // empty
        'students' => [
            [
                'id' => $student_1->id,
                'class_enter_date' => '2024-07-01',
                'is_active' => true,
            ],
        ]
    ];

    $this->classService->assignClassToStudent($payload);

    expect($semester_class->refresh()->activeStudentClasses)->toHaveCount(1);

    $this->assertDatabaseCount('student_classes', 2);

    $this->assertDatabaseHas('student_classes', [
        'semester_setting_id' => $payload['semester_setting_id'],
        'semester_class_id' => $payload['semester_class_id'],
        'student_id' => $payload['students'][0]['id'],
        'class_enter_date' => $payload['students'][0]['class_enter_date'],
        'is_active' => $payload['students'][0]['is_active'],
    ]);

    // assert student 2 to be is_active = false
    $this->assertDatabaseHas('student_classes', [
        'semester_setting_id' => $payload['semester_setting_id'],
        'semester_class_id' => $payload['semester_class_id'],
        'student_id' => $student_2->id,
        'class_enter_date' => '2024-07-01',
        'is_active' => false,
        'is_latest_class_in_semester' => false
    ]);


    /** REASSIGN STUDENT $student_2->id TO THE SEMESTER_CLASS -> expect 3 student_class, for $student_2->id : 1 is_active = true, 1 is_active = false  */
    $payload = [
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class->id,
        'class_subject_ids' => [], // empty
        'students' => [
            [
                'id' => $student_1->id,
                'class_enter_date' => '2024-07-01',
                'is_active' => true,
            ],
            [
                'id' => $student_2->id, // re-add $student_2 to this semester_class->id
                'class_enter_date' => '2024-07-02', // new class_date for student 2
                'is_active' => true,
            ]
        ]
    ];

    $this->classService->assignClassToStudent($payload);

    expect($semester_class->refresh()->activeStudentClasses)->toHaveCount(2);

    $this->assertDatabaseCount('student_classes', 3);

    $this->assertDatabaseHas('student_classes', [
        'semester_setting_id' => $payload['semester_setting_id'],
        'semester_class_id' => $payload['semester_class_id'],
        'student_id' => $payload['students'][0]['id'],
        'class_enter_date' => $payload['students'][0]['class_enter_date'],
        'is_active' => $payload['students'][0]['is_active'],
    ]);

    // assert student 2 previous record to be is_active = false
    $this->assertDatabaseHas('student_classes', [
        'semester_setting_id' => $payload['semester_setting_id'],
        'semester_class_id' => $payload['semester_class_id'],
        'student_id' => $student_2->id,
        'class_enter_date' => '2024-07-01',
        'is_active' => false,
    ]);

    // assert student 2 new record to be is_active = true
    $this->assertDatabaseHas('student_classes', [
        'semester_setting_id' => $payload['semester_setting_id'],
        'semester_class_id' => $payload['semester_class_id'],
        'student_id' => $student_2->id,
        'class_enter_date' => $payload['students'][1]['class_enter_date'],
        'is_active' => true,
    ]);


    /** REMOVE ALL STUDENT FROM THE SEMESTER_CLASS -> expect all student_class is_active = false */
    $payload = [
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class->id,
        'class_subject_ids' => [], // empty
        'students' => [], // empty
    ];

    $this->classService->assignClassToStudent($payload);

    $this->assertDatabaseCount('student_classes', 3);

    expect($semester_class->refresh()->activeStudentClasses)->toHaveCount(0);

    $student_classes = $semester_class->studentClasses;

    foreach ($student_classes as $student_class) {
        expect($student_class->is_active)->toBeFalsy();
    }
});


test('assignClassToStudent() - students moved to another class', function () {
    $sem_year = SemesterYearSetting::create([
        'year' => 2025
    ]);

    $sem1 = SemesterSetting::factory()->create([
        'name' => '2025 Semester 1',
        'semester_year_setting_id' => $sem_year->id,
    ]);

    $grades = Grade::factory(2)->create(new Sequence(
        [
            'name->en' => 'Junior 1'
        ],
    ));

    $classes = ClassModel::factory(2)->create(new Sequence(
        [
            'name->en' => 'J1001',
            'type' => ClassType::PRIMARY,
            'grade_id' => $grades[0]->id
        ],
        [
            'name->en' => 'J1002',
            'type' => ClassType::PRIMARY,
            'grade_id' => $grades[0]->id
        ],
    ));

    $semester_classes = SemesterClass::factory(2)->create(new Sequence(
        [
            'semester_setting_id' => $sem1->id,
            'class_id' => $classes[0]->id // J1001
        ],
        [
            'semester_setting_id' => $sem1->id,
            'class_id' => $classes[1]->id // J1002
        ],
    ));

    $students = Student::factory(4)->create(new Sequence(
        [
            'name->en' => 'John Jones',
            'gender' => Gender::MALE->value,
        ],
        [
            'name->en' => 'Dwayne',
            'gender' => Gender::MALE->value,
        ],
        [
            'name->en' => 'Frank',
            'gender' => Gender::MALE->value,
        ],
        [
            'name->en' => 'Charlie',
            'gender' => Gender::MALE->value,
        ],
    ));

    $this->assertDatabaseCount('student_classes', 0);


    // assign 4 students under $semester_classes[0]
    $payload = [
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id,
        'students' => [
            [
                'id' => $students[0]->id,
                'seat_no' => 1,
                'is_active' => true,
                'class_enter_date' => '2024-08-08',
            ],
            [
                'id' => $students[1]->id,
                'seat_no' => 2,
                'is_active' => true,
                'class_enter_date' => '2024-08-08',
            ],
            [
                'id' => $students[2]->id,
                'seat_no' => 3,
                'is_active' => true,
                'class_enter_date' => '2024-08-08',
            ],
            [
                'id' => $students[3]->id,
                'seat_no' => 4,
                'is_active' => true,
                'class_enter_date' => '2024-08-08',
            ],
        ]
    ];

    $this->classService->assignClassToStudent($payload);

    expect($semester_classes[0]->refresh()->activeStudentClasses)->toHaveCount(4);
    expect($semester_classes[1]->refresh()->activeStudentClasses)->toHaveCount(0);

    $this->assertDatabaseCount('student_classes', 4);

    $this->assertDatabaseHas('student_classes', [
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id,
        'class_type' => 'PRIMARY',
        'student_id' => $students[0]->id,
        'class_enter_date' => '2024-08-08',
        'is_active' => true,
        'is_latest_class_in_semester' => true
    ]);

    $this->assertDatabaseHas('student_classes', [
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id,
        'class_type' => 'PRIMARY',
        'student_id' => $students[1]->id,
        'class_enter_date' => '2024-08-08',
        'is_active' => true,
        'is_latest_class_in_semester' => true
    ]);

    $this->assertDatabaseHas('student_classes', [
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id,
        'class_type' => 'PRIMARY',
        'student_id' => $students[2]->id,
        'class_enter_date' => '2024-08-08',
        'is_active' => true,
        'is_latest_class_in_semester' => true
    ]);

    $this->assertDatabaseHas('student_classes', [
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id,
        'class_type' => 'PRIMARY',
        'student_id' => $students[3]->id,
        'class_enter_date' => '2024-08-08',
        'is_active' => true,
        'is_latest_class_in_semester' => true
    ]);


    /**
     *
     * REASSIGN students[0] and students[1] to $semester_classes[1]
     * class_enter_date is same as previous class_enter_date
     */
    $payload = [
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[1]->id,
        'students' => [
            [
                'id' => $students[0]->id,
                'seat_no' => 1,
                'is_active' => true,
                'class_enter_date' => '2024-08-08',
            ],
            [
                'id' => $students[1]->id,
                'seat_no' => 2,
                'is_active' => true,
                'class_enter_date' => '2024-08-08',
            ]
        ]
    ];

    $this->classService->assignClassToStudent($payload);

    expect($semester_classes[0]->refresh()->activeStudentClasses)->toHaveCount(2);
    expect($semester_classes[1]->refresh()->activeStudentClasses)->toHaveCount(2);

    $this->assertDatabaseCount('student_classes', 4);

    $this->assertDatabaseHas('student_classes', [
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[1]->id, // $students[0] change to $semester_classes[1]
        'class_type' => 'PRIMARY',
        'student_id' => $students[0]->id,
        'class_enter_date' => '2024-08-08',
        'is_active' => true,
        'is_latest_class_in_semester' => true
    ]);

    $this->assertDatabaseHas('student_classes', [
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[1]->id, // $students[1] change to $semester_classes[1]
        'class_type' => 'PRIMARY',
        'student_id' => $students[1]->id,
        'class_enter_date' => '2024-08-08',
        'is_active' => true,
        'is_latest_class_in_semester' => true
    ]);

    $this->assertDatabaseHas('student_classes', [
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id,
        'class_type' => 'PRIMARY',
        'student_id' => $students[2]->id,
        'class_enter_date' => '2024-08-08',
        'is_active' => true,  // old record for $students[2] remained
        'is_latest_class_in_semester' => true
    ]);

    $this->assertDatabaseHas('student_classes', [
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id,
        'class_type' => 'PRIMARY',
        'student_id' => $students[3]->id,
        'class_enter_date' => '2024-08-08',
        'is_active' => true,  // old record for $students[3] remained
        'is_latest_class_in_semester' => true
    ]);


    /**
     *
     * REASSIGN students[0] and students[1] back to $semester_classes[0]
     * class_enter_date is next day
     */
    $payload = [
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id,
        'students' => [
            [
                'id' => $students[0]->id,
                'seat_no' => 1,
                'is_active' => true,
                'class_enter_date' => '2024-08-09',
            ],
            [
                'id' => $students[1]->id,
                'seat_no' => 2,
                'is_active' => true,
                'class_enter_date' => '2024-08-09',
            ],
            [
                'id' => $students[2]->id,   // old record remained
                'seat_no' => 3,
                'is_active' => true,
                'class_enter_date' => '2024-08-08',
            ],
            [
                'id' => $students[3]->id,   // old record remained
                'seat_no' => 4,
                'is_active' => true,
                'class_enter_date' => '2024-08-08',
            ],
        ]
    ];

    $this->classService->assignClassToStudent($payload);

    expect($semester_classes[0]->refresh()->activeStudentClasses)->toHaveCount(4);
    expect($semester_classes[1]->refresh()->activeStudentClasses)->toHaveCount(0);

    $this->assertDatabaseCount('student_classes', 6);

    // old record for $students[0]->id from $semester_classes[1] will be inactive
    $this->assertDatabaseHas('student_classes', [
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[1]->id,
        'class_type' => 'PRIMARY',
        'student_id' => $students[0]->id,
        'class_enter_date' => '2024-08-08',
        'is_active' => false,
        'is_latest_class_in_semester' => false
    ]);

    // old record for $students[1]->id from $semester_classes[1] will be inactive
    $this->assertDatabaseHas('student_classes', [
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[1]->id,
        'class_type' => 'PRIMARY',
        'student_id' => $students[1]->id,
        'class_enter_date' => '2024-08-08',
        'is_active' => false,
        'is_latest_class_in_semester' => false
    ]);

    // old record for $students[2]->id from $semester_classes[0] remained
    $this->assertDatabaseHas('student_classes', [
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id,
        'class_type' => 'PRIMARY',
        'student_id' => $students[2]->id,
        'class_enter_date' => '2024-08-08',
        'is_active' => true,
        'is_latest_class_in_semester' => true
    ]);

    // old record for $students[3]->id from $semester_classes[0] remained
    $this->assertDatabaseHas('student_classes', [
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id,
        'class_type' => 'PRIMARY',
        'student_id' => $students[3]->id,
        'class_enter_date' => '2024-08-08',
        'is_active' => true,
        'is_latest_class_in_semester' => true
    ]);

    // new record for $students[0] from $semester_classes[0]
    $this->assertDatabaseHas('student_classes', [
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id,
        'class_type' => 'PRIMARY',
        'student_id' => $students[0]->id,
        'class_enter_date' => '2024-08-09',
        'is_active' => true,  // new record is_active = true
        'is_latest_class_in_semester' => true
    ]);

    // new record for $students[1] from $semester_classes[0]
    $this->assertDatabaseHas('student_classes', [
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id,
        'class_type' => 'PRIMARY',
        'student_id' => $students[1]->id,
        'class_enter_date' => '2024-08-09',
        'is_active' => true,  // new record is_active = true
        'is_latest_class_in_semester' => true
    ]);
});

test('assignSemestersToClass()', function () {
    $class = ClassModel::factory()->create();

    $semester_setting_1 = SemesterSetting::factory()->create();
    $semester_setting_2 = SemesterSetting::factory()->create();
    $semester_setting_3 = SemesterSetting::factory()->create();

    $homeroom_teacher_1 = Employee::factory()->create();
    $homeroom_teacher_2 = Employee::factory()->create();
    $homeroom_teacher_3 = Employee::factory()->create();

    // initially assign only one semester_class and homeroom_teacher_id null
    $payload = [
        'class_id' => $class->id,
        'semester_settings' => [
            [
                'id' => $semester_setting_1->id,
                'homeroom_teacher_id' => null,
                'is_active' => true,
            ],
        ],
    ];

    $response = $this->classService->assignSemestersToClass($payload);

    expect($response)->toBeNull();

    $this->assertDatabaseCount('semester_classes', 1);

    $this->assertDatabaseHas('semester_classes', [
        'semester_setting_id' => $payload['semester_settings'][0]['id'],
        'class_id' => $payload['class_id'],
        'homeroom_teacher_id' => $payload['semester_settings'][0]['homeroom_teacher_id'],
        'is_active' => $payload['semester_settings'][0]['is_active'],
    ]);

    // assign 2 more semester_class and update old homeroom_teacher_id to be not null
    $payload = [
        'class_id' => $class->id,
        'semester_settings' => [
            [
                'id' => $semester_setting_1->id,
                'homeroom_teacher_id' => $homeroom_teacher_1->id, // add teacher now
                'is_active' => true,
            ],
            [
                'id' => $semester_setting_2->id,
                'homeroom_teacher_id' => $homeroom_teacher_2->id,
                'is_active' => true,
            ],
            [
                'id' => $semester_setting_3->id,
                'homeroom_teacher_id' => $homeroom_teacher_3->id,
                'is_active' => true,
            ],
        ],
    ];

    $response = $this->classService->assignSemestersToClass($payload);

    expect($response)->toBeNull();

    $this->assertDatabaseCount('semester_classes', 3);

    foreach ($payload['semester_settings'] as $semester_setting_payload) {
        $this->assertDatabaseHas('semester_classes', [
            'semester_setting_id' => $semester_setting_payload['id'],
            'class_id' => $payload['class_id'],
            'homeroom_teacher_id' => $semester_setting_payload['homeroom_teacher_id'],
            'is_active' => $semester_setting_payload['is_active'],
        ]);
    }

    // semester_class can only be removed from destroy API, even if dont provide here, still no effect to existing class->semesterClasses
    $payload = [
        'class_id' => $class->id,
        'semester_settings' => [
            [
                'id' => $semester_setting_1->id,
                'homeroom_teacher_id' => $homeroom_teacher_1->id, // add teacher now
                'is_active' => true,
            ],
        ],
    ];

    $this->classService->assignSemestersToClass($payload);

    $this->assertDatabaseCount('semester_classes', 3);

    $this->assertDatabaseHas('semester_classes', [
        'semester_setting_id' => $semester_setting_1->id,
        'class_id' => $payload['class_id'],
        'homeroom_teacher_id' => $homeroom_teacher_1->id,
        'is_active' => true,
    ]);

    // Make sure the other 2 semester_class is still remained same as previous
    $this->assertDatabaseHas('semester_classes', [
        'semester_setting_id' => $semester_setting_2->id,
        'homeroom_teacher_id' => $homeroom_teacher_2->id,
        'is_active' => true,
    ]);

    $this->assertDatabaseHas('semester_classes', [
        'semester_setting_id' => $semester_setting_3->id,
        'homeroom_teacher_id' => $homeroom_teacher_3->id,
        'is_active' => true,
    ]);
});

test('getStudentsBySemesterClassId()', function () {
    $students = Student::factory(2)->state(new Sequence(
        [
            'name->en' => 'Jon',
        ],
        [
            'name->en' => 'Jack',
        ],
    ))->create();

    $semester_setting = SemesterSetting::factory()->create([
        'name' => '2024 Semester 1',
        'is_current_semester' => true,
    ]);

    $classes = ClassModel::factory(2)->state(new Sequence(
        [
            'name->en' => 'K100',
            'type' => ClassType::PRIMARY,
        ],
        [
            'name->en' => 'K200',
            'type' => ClassType::PRIMARY,
        ],
    ))->create();

    $semester_classes = SemesterClass::factory(2)->state(new Sequence(
        [
            'semester_setting_id' => $semester_setting->id,
            'class_id' => $classes[0]->id,
            'is_active' => true,
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'class_id' => $classes[1]->id,
            'is_active' => true,
        ],
    ))->create();

    $student_classes = StudentClass::factory(2)->state(new Sequence(
    // semester class 1
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[0]->id,
            'class_type' => ClassType::PRIMARY,
            'student_id' => $students[0]->id,
            'seat_no' => 1,
            'is_active' => true, // Jon
        ],

        // semester class 2
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[1]->id,
            'class_type' => ClassType::PRIMARY,
            'student_id' => $students[1]->id,
            'seat_no' => 1,
            'is_active' => true, // Jack
        ],
    ))->create();


    /**
     *
     * FIlter by $semester_classes[0]->id
     *
     */
    $response = $this->classService->getStudentsBySemesterClassId($semester_classes[0]->id)->toArray();

    expect($response[0])->toMatchArray($student_classes[0]->toArray())
        ->toHaveKey('student', $students[0]->toArray());

    /**
     *
     * FIlter by $semester_classes[1]->id
     *
     */
    $response = $this->classService->getStudentsBySemesterClassId($semester_classes[1]->id)->toArray();

    expect($response[0])->toMatchArray($student_classes[1]->toArray())
        ->toHaveKey('student', $students[1]->toArray());
});


test('assignSeats()', function () {
    $students = Student::factory(3)->state(new Sequence(
        [
            'name->en' => 'Jon',
        ],
        [
            'name->en' => 'Jack',
        ],
        [
            'name->en' => 'Cavill',
        ],
    ))->create();

    $semester_setting = SemesterSetting::factory()->create([
        'name' => '2024 Semester 1',
        'is_current_semester' => true,
    ]);

    $classes = ClassModel::factory(2)->state(new Sequence(
        [
            'name->en' => 'K100',
            'type' => ClassType::PRIMARY,
        ],
        [
            'name->en' => 'K200',
            'type' => ClassType::PRIMARY,
        ],
    ))->create();

    $semester_classes = SemesterClass::factory(2)->state(new Sequence(
        [
            'semester_setting_id' => $semester_setting->id,
            'class_id' => $classes[0]->id,
            'is_active' => true,
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'class_id' => $classes[1]->id,
            'is_active' => true,
        ],
    ))->create();

    $student_classes = StudentClass::factory(3)->state(new Sequence(
    // semester class 1
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[0]->id,
            'class_type' => ClassType::PRIMARY,
            'student_id' => $students[0]->id,
            'seat_no' => 1,
            'is_active' => true, // Jon
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[0]->id,
            'class_type' => ClassType::PRIMARY,
            'student_id' => $students[1]->id,
            'seat_no' => 2,
            'is_active' => true, // Jack
        ],

        // semester class 2
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[1]->id,
            'class_type' => ClassType::PRIMARY,
            'student_id' => $students[2]->id,
            'seat_no' => 1,
            'is_active' => true, // Cavill
        ],
    ))->create();


    /**
     *
     * Update Jon and Jack seat from $semester_classes[0]->id
     *
     */

    $payload = [
        [
            'student_class_id' => $student_classes[0]->id,
            'seat_no' => 10,
        ],
        [
            'student_class_id' => $student_classes[1]->id,
            'seat_no' => 20,
        ],
    ];

    $this->classService->assignSeats($payload);

    $this->assertDatabaseHas('student_classes', [
        'id' => $payload[0]['student_class_id'],
        'seat_no' => 10,
    ]);

    $this->assertDatabaseHas('student_classes', [
        'id' => $payload[1]['student_class_id'],
        'seat_no' => 20,
    ]);

    // assert Cavill seat remain
    $this->assertDatabaseHas('student_classes', [
        'id' => $student_classes[2]->id,
        'seat_no' => 1,
    ]);


    /**
     *
     * Update Cavill seat from $semester_classes[1]->id
     *
     */

    $payload = [
        [
            'student_class_id' => $student_classes[2]->id,
            'seat_no' => 77,
        ],
    ];

    $this->classService->assignSeats($payload);

    // assert Cavill seat updated
    $this->assertDatabaseHas('student_classes', [
        'id' => $payload[0]['student_class_id'],
        'seat_no' => 77,
    ]);

    // assert Jon seat remained
    $this->assertDatabaseHas('student_classes', [
        'id' => $student_classes[0]->id,
        'seat_no' => 10,
    ]);

    // assert Jack seat remained
    $this->assertDatabaseHas('student_classes', [
        'id' => $student_classes[1]->id,
        'seat_no' => 20,
    ]);
});


test('autoAssignSeatsBySemester()', function () {
    $students = Student::factory(6)->state(new Sequence(
        [
            'name->en' => 'Serena', // Serena is in 2 class
        ],
        [
            'name->en' => 'Jon',
        ],
        [
            'name->en' => 'Jack',
        ],
        [
            'name->en' => 'Paul',
        ],
        [
            'name->en' => 'David',
        ],
        [
            'name->en' => 'Cavill',
        ],
    ))->create();

    $semester_setting = SemesterSetting::factory()->create([
        'name' => '2024 Semester 1',
        'is_current_semester' => true,
    ]);

    $classes = ClassModel::factory(2)->state(new Sequence(
        [
            'name->en' => 'K100',
            'type' => ClassType::PRIMARY,
        ],
        [
            'name->en' => 'K200',
            'type' => ClassType::PRIMARY,
        ],
    ))->create();

    $semester_classes = SemesterClass::factory(2)->state(new Sequence(
        [
            'semester_setting_id' => $semester_setting->id,
            'class_id' => $classes[0]->id,
            'is_active' => true,
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'class_id' => $classes[1]->id,
            'is_active' => true,
        ],
    ))->create();

    $student_classes = StudentClass::factory(7)->state(new Sequence(
    // semester class 1
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[0]->id,
            'class_type' => ClassType::PRIMARY,
            'student_id' => $students[0]->id,
            'seat_no' => null,
            'is_active' => true, // Serena
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[0]->id,
            'class_type' => ClassType::PRIMARY,
            'student_id' => $students[1]->id,
            'seat_no' => null,
            'is_active' => true, // Jon
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[0]->id,
            'class_type' => ClassType::PRIMARY,
            'student_id' => $students[2]->id,
            'seat_no' => null,
            'is_active' => true, // Jack
        ],

        // semester class 2
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[1]->id,
            'class_type' => ClassType::PRIMARY,
            'student_id' => $students[3]->id,
            'seat_no' => null,
            'is_active' => true, // Paul
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[1]->id,
            'class_type' => ClassType::PRIMARY,
            'student_id' => $students[4]->id,
            'seat_no' => null,
            'is_active' => true, // David
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[1]->id,
            'class_type' => ClassType::PRIMARY,
            'student_id' => $students[5]->id,
            'seat_no' => null,
            'is_active' => true, // Cavill
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[1]->id,
            'class_type' => ClassType::PRIMARY,
            'student_id' => $students[0]->id,
            'seat_no' => null,
            'is_active' => true, // Serena
        ],
    ))->create();


    /**
     *
     * expect in $semester_classes[0] : seat_no : Jack , Jon, Serena
     *
     *
     * expect in $semester_classes[1] : seat_no : Cavill, David, Paul, Serena
     *
     */

    $this->classService->autoAssignSeatsBySemester($semester_setting->id);


    /**
     * validate $semester_classes[0]
     */

    // Jack semester_class_1, seat_no, 1
    $student_classes[2]->refresh();

    expect($student_classes[2]->seat_no)->toEqual(1)
        ->and($student_classes[2]->semester_class_id)->toEqual($semester_classes[0]->id)
        ->and($student_classes[2]->student->getTranslation('name', 'en'))->toEqual('Jack');

    // Jon semester_class_1, seat_no, 2
    $student_classes[1]->refresh();

    expect($student_classes[1]->seat_no)->toEqual(2)
        ->and($student_classes[1]->semester_class_id)->toEqual($semester_classes[0]->id)
        ->and($student_classes[1]->student->getTranslation('name', 'en'))->toEqual('Jon');

    // Serena semester_class_1, seat_no, 3
    $student_classes[0]->refresh();

    expect($student_classes[0]->seat_no)->toEqual(3)
        ->and($student_classes[0]->semester_class_id)->toEqual($semester_classes[0]->id)
        ->and($student_classes[0]->student->getTranslation('name', 'en'))->toEqual('Serena');


    /**
     * validate $semester_classes[1]
     */

    // Cavill semester_class_2, seat_no, 1
    $student_classes[5]->refresh();

    expect($student_classes[5]->seat_no)->toEqual(1)
        ->and($student_classes[5]->semester_class_id)->toEqual($semester_classes[1]->id)
        ->and($student_classes[5]->student->getTranslation('name', 'en'))->toEqual('Cavill');

    // David semester_class_2, seat_no, 2
    $student_classes[4]->refresh();

    expect($student_classes[4]->seat_no)->toEqual(2)
        ->and($student_classes[4]->semester_class_id)->toEqual($semester_classes[1]->id)
        ->and($student_classes[4]->student->getTranslation('name', 'en'))->toEqual('David');

    // Paul semester_class_2, seat_no, 3
    $student_classes[3]->refresh();

    expect($student_classes[3]->seat_no)->toEqual(3)
        ->and($student_classes[3]->semester_class_id)->toEqual($semester_classes[1]->id)
        ->and($student_classes[3]->student->getTranslation('name', 'en'))->toEqual('Paul');

    // Serena semester_class_2, seat_no, 4
    $student_classes[6]->refresh();

    expect($student_classes[6]->seat_no)->toEqual(4)
        ->and($student_classes[6]->semester_class_id)->toEqual($semester_classes[1]->id)
        ->and($student_classes[6]->student->getTranslation('name', 'en'))->toEqual('Serena');


    /**
     *
     * re assign again for this semester, expect to fail
     *
     */

    expect(function () use ($semester_setting) {
        $this->classService->autoAssignSeatsBySemester($semester_setting->id);
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(6006);
    }, __('system_error.6006'));
});

test('assignClassesToSemesters()', function () {
    $class_1 = ClassModel::factory()->create();
    $class_2 = ClassModel::factory()->create();

    $semester_setting_1 = SemesterSetting::factory()->create();
    $semester_setting_2 = SemesterSetting::factory()->create();

    $existing_semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting_1->id,
        'class_id' => $class_1->id,
    ]);

    $payload = [
        'semester_setting_ids' => [
            $semester_setting_1->id,
            $semester_setting_2->id,
        ],
        'class_ids' => [
            $class_1->id,
            $class_2->id,
        ],
    ];

    $this->assertDatabaseCount('semester_classes', 1);

    $this->assertDatabaseHas('semester_classes', [
        'id' => $existing_semester_class->id,
        'class_id' => $class_1->id,
        'semester_setting_id' => $semester_setting_1->id,
    ]);

    /**
     *
     * expect existing combination in payload is ignored
     * expect to create 3 new semester_classes
     *
     */

    $this->classService->assignClassesToSemesters($payload);

    $this->assertDatabaseCount('semester_classes', 4);

    // old record remained
    $this->assertDatabaseHas('semester_classes', [
        'id' => $existing_semester_class->id,
        'class_id' => $existing_semester_class->class_id,
        'semester_setting_id' => $existing_semester_class->semester_setting_id,
    ]);

    // new record 1
    $this->assertDatabaseHas('semester_classes', [
        'class_id' => $class_1->id,
        'semester_setting_id' => $semester_setting_2->id,
    ]);

    // new record 2
    $this->assertDatabaseHas('semester_classes', [
        'class_id' => $class_2->id,
        'semester_setting_id' => $semester_setting_1->id,
    ]);

    // new record 3
    $this->assertDatabaseHas('semester_classes', [
        'class_id' => $class_2->id,
        'semester_setting_id' => $semester_setting_2->id,
    ]);
});
