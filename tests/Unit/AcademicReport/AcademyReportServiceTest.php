<?php

use App\Models\ClassModel;
use App\Models\Exam;
use App\Models\Grade;
use App\Models\GradingScheme;
use App\Models\ResultSource;
use App\Models\ResultSourceExam;
use App\Models\ResultSourceSubject;
use App\Models\ResultSourceSubjectComponent;
use App\Models\ResultsPostingHeader;
use App\Models\ResultsPostingLineItem;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\SemesterYearSetting;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\StudentGradingFramework;
use App\Models\StudentReportCard;
use App\Models\Subject;
use App\Services\Reports\AcademyReportService;
use Database\Seeders\InternationalizationSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Support\Facades\Cache;

beforeEach(function () {
    Cache::clear();
    $this->seed(InternationalizationSeeder::class);

    $this->academic_year_2025 = '2025';

    $this->config = json_decode(file_get_contents(storage_path('tests/grading-framework-config-1.json')), true);

    $this->service = app(AcademyReportService::class);
});

test('getNetAveragePassingRateReportData', function () {

    $grading_scheme = GradingScheme::factory()->create();

    $semester_setting_1 = SemesterSetting::factory()->create(['name' => '2025 Sem 1']);
    $semester_setting_2 = SemesterSetting::factory()->create(['name' => '2025 Sem 2']);

    $grade_1 = Grade::factory()->create(['name->en' => 'J1']);
    $grade_2 = Grade::factory()->create(['name->en' => 'J2']);
    $grade_3 = Grade::factory()->create(['name->en' => 'J3']);

    $grades = Grade::all();

    foreach ($grades as $grade){
        // Create Classes and Semester Classes (3 per grade, per semester, all Junior Grade)
        $classes = ClassModel::factory(3)->state(new Sequence(
            ['code' => $grade->name.'11', 'name->en' => $grade->name.'11', 'grade_id' => $grade->id,],
            ['code' => $grade->name.'12', 'name->en' => $grade->name.'12','grade_id' => $grade->id],
            ['code' => $grade->name.'13', 'name->en' => $grade->name.'13','grade_id' => $grade->id],
        ))->create();

        $sem1_classes = SemesterClass::factory(3)->state(new Sequence(
            ['semester_setting_id' => $semester_setting_1->id, 'class_id' => $classes[0]->id],
            ['semester_setting_id' => $semester_setting_1->id, 'class_id' => $classes[1]->id],
            ['semester_setting_id' => $semester_setting_1->id, 'class_id' => $classes[2]->id]
        ))->create();

        $sem2_classes = SemesterClass::factory(3)->state(new Sequence(
            ['semester_setting_id' => $semester_setting_2->id, 'class_id' => $classes[0]->id],
            ['semester_setting_id' => $semester_setting_2->id, 'class_id' => $classes[1]->id],
            ['semester_setting_id' => $semester_setting_2->id, 'class_id' => $classes[2]->id]
        ))->create();

        // Creating posting headers, 3 per code, linked with one active report card
        $sem1_header = ResultsPostingHeader::factory()->create([
            'grade_id' => $grade->id,
            'semester_setting_id' => $semester_setting_1->id,
            'report_card_output_code' => 'SEM1RESULT'
        ]);

        $sem2_header = ResultsPostingHeader::factory()->create([
            'grade_id' => $grade->id,
            'semester_setting_id' => $semester_setting_2->id,
            'report_card_output_code' => 'SEM2RESULT'
        ]);

        $final_header = ResultsPostingHeader::factory()->create([
            'grade_id' => $grade->id,
            'semester_setting_id' => $semester_setting_2->id,
            'report_card_output_code' => 'FINALRESULT'
        ]);

        StudentReportCard::factory()->create(['results_posting_header_id' => $sem1_header->id]);
        StudentReportCard::factory()->create(['results_posting_header_id' => $sem2_header->id]);
        StudentReportCard::factory()->create(['results_posting_header_id' => $final_header->id]);

        // Create Result Posting Line Items, each class will have 4 students, 1 student pass
        foreach ($sem1_classes as $sem_class){
            ResultsPostingLineItem::factory(3)->create([
                'header_id' => $sem1_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'student_id' => Student::factory()->create()->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'total' => 70,
            ]);

            ResultsPostingLineItem::factory()->create([
                'header_id' => $sem1_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'student_id' => Student::factory()->create()->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'total' => 50,
            ]);
        }

        foreach ($sem2_classes as $sem_class){
            ResultsPostingLineItem::factory(3)->create([
                'header_id' => $sem2_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'student_id' => Student::factory()->create()->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'total' => 70,
            ]);

            ResultsPostingLineItem::factory()->create([
                'header_id' => $sem2_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'student_id' => Student::factory()->create()->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'total' => 50,
            ]);
        }

        foreach ($sem2_classes as $sem_class){
            ResultsPostingLineItem::factory(3)->create([
                'header_id' => $final_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'student_id' => Student::factory()->create()->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'total' => 70,
            ]);

            ResultsPostingLineItem::factory()->create([
                'header_id' => $final_header->id,
                'report_card_output_component_code' => 'SYS_NET_AVG',
                'student_id' => Student::factory()->create()->id,
                'subject_id' => null,
                'grade_id' => $grade->id,
                'grading_scheme_id' => $grading_scheme->id,
                'semester_class_id' => $sem_class->id,
                'total' => 50,
            ]);
        }
    }

    // Get J1 grade only SEM1, expect all to be 75% pass rate (1 student failed in every class)
    $headers = ResultsPostingHeader::select('id')
        ->where([
        'grade_id' => $grade_1->id,
        'semester_setting_id' => $semester_setting_1->id,
        'report_card_output_code' => 'SEM1RESULT'
        ])
        ->get();

    $filters = [
        'grade_ids' => [$grade_1->id ],
        'semester_setting_id' => $semester_setting_1->id,
        'result_posting_header_ids' => $headers
    ];

    $response = $this->service->getNetAveragePassingRateReportData($filters);
    expect($response)->toMatchArray([
        "title" => "2025 Sem 1 J1 Net Average Passing Rate Report",
        "report_data" => [
            'J111' => '75.00',
            'J112' => '75.00',
            'J113' => '75.00'
        ]
    ]);

    // Test J1, J2, J3 grade SEM1RESULT, expect all to be 75% pass rate
    $headers = ResultsPostingHeader::select('id')
        ->where([
            'semester_setting_id' => $semester_setting_1->id,
            'report_card_output_code' => 'SEM1RESULT'
        ])
        ->whereIn('grade_id', [$grade_1->id, $grade_2->id, $grade_3->id])
        ->get();

    $filters = [
        'grade_ids' => [$grade_1->id, $grade_2->id, $grade_3->id],
        'semester_setting_id' => $semester_setting_1->id,
        'result_posting_header_ids' => $headers
    ];

    $response = $this->service->getNetAveragePassingRateReportData($filters);

    expect($response)->toMatchArray([
        "title" => "2025 Sem 1 J1, J2, J3 Net Average Passing Rate Report",
        "report_data" => [
            'J111' => '75.00',
            'J112' => '75.00',
            'J113' => '75.00',
            'J211' => '75.00',
            'J212' => '75.00',
            'J213' => '75.00',
            'J311' => '75.00',
            'J312' => '75.00',
            'J313' => '75.00'
        ]
    ]);

    // J1 only, SEM2RESULT, expect all 75%
    $headers = ResultsPostingHeader::select('id')
        ->where([
            'semester_setting_id' => $semester_setting_2->id,
            'report_card_output_code' => 'SEM2RESULT'
        ])
        ->whereIn('grade_id', [$grade_1->id])
        ->get();

    $filters = [
        'grade_ids' => [$grade_1->id ],
        'semester_setting_id' => $semester_setting_2->id,
        'result_posting_header_ids' => $headers
    ];

    $response = $this->service->getNetAveragePassingRateReportData($filters);
    expect($response)->toMatchArray([
        "title" => "2025 Sem 2 J1 Net Average Passing Rate Report",
        "report_data" => [
            'J111' => '75.00',
            'J112' => '75.00',
            'J113' => '75.00'
        ]
    ]);

    // J1, J2, J3, SEM2RESULT, expect all 75%
    $headers = ResultsPostingHeader::select('id')
        ->where([
            'semester_setting_id' => $semester_setting_2->id,
            'report_card_output_code' => 'SEM2RESULT'
        ])
        ->whereIn('grade_id', [$grade_1->id, $grade_2->id, $grade_3->id])
        ->get();

    $filters = [
        'grade_ids' => [$grade_1->id, $grade_2->id, $grade_3->id],
        'semester_setting_id' => $semester_setting_2->id,
        'result_posting_header_ids' => $headers
    ];

    $response = $this->service->getNetAveragePassingRateReportData($filters);
    expect($response)->toMatchArray([
        "title" => "2025 Sem 2 J1, J2, J3 Net Average Passing Rate Report",
        "report_data" => [
            'J111' => '75.00',
            'J112' => '75.00',
            'J113' => '75.00',
            'J211' => '75.00',
            'J212' => '75.00',
            'J213' => '75.00',
            'J311' => '75.00',
            'J312' => '75.00',
            'J313' => '75.00'
        ]
    ]);

    // J1 only, FINALRESULT
    $headers = ResultsPostingHeader::select('id')
        ->where([
            'semester_setting_id' => $semester_setting_2->id,
            'report_card_output_code' => 'FINALRESULT'
        ])
        ->whereIn('grade_id', [$grade_1->id])
        ->get();


    $filters = [
        'grade_ids' => [$grade_1->id ],
        'semester_setting_id' => $semester_setting_2->id,
        'result_posting_header_ids' => $headers
    ];

    $response = $this->service->getNetAveragePassingRateReportData($filters);
    expect($response)->toMatchArray([
        "title" => "2025 Sem 2 J1 Net Average Passing Rate Report",
        "report_data" => [
            'J111' => '75.00',
            'J112' => '75.00',
            'J113' => '75.00'
        ]
    ]);

    // J1, J2, J3, FINALRESLT
    $headers = ResultsPostingHeader::select('id')
        ->where([
            'semester_setting_id' => $semester_setting_2->id,
            'report_card_output_code' => 'FINALRESULT'
        ])
        ->whereIn('grade_id', [$grade_1->id, $grade_2->id, $grade_3->id])
        ->get();

    $filters = [
        'grade_ids' => [$grade_1->id, $grade_2->id, $grade_3->id],
        'semester_setting_id' => $semester_setting_2->id,
        'result_posting_header_ids' => $headers
    ];

    $response = $this->service->getNetAveragePassingRateReportData($filters);
    expect($response)->toMatchArray([
        "title" => "2025 Sem 2 J1, J2, J3 Net Average Passing Rate Report",
        "report_data" => [
            'J111' => '75.00',
            'J112' => '75.00',
            'J113' => '75.00',
            'J211' => '75.00',
            'J212' => '75.00',
            'J213' => '75.00',
            'J311' => '75.00',
            'J312' => '75.00',
            'J313' => '75.00'
        ]
    ]);

    // wrong semester setting, should return null
    $headers = ResultsPostingHeader::select('id')
        ->where([
            'semester_setting_id' => $semester_setting_1->id,
            'report_card_output_code' => 'FINALRESULT'
        ])
        ->whereIn('grade_id', [$grade_1->id, $grade_2->id, $grade_3->id])
        ->get();

    $filters = [
        'grade_ids' => [$grade_1->id, $grade_2->id, $grade_3->id],
        'semester_setting_id' => $semester_setting_1->id,
        'result_posting_header_ids' => $headers
    ];

    $response = $this->service->getNetAveragePassingRateReportData($filters);
    expect($response['report_data'])->toBeEmpty();

    // Set J111 and J113 to 50% pass rate (test calculations)
    $line_item_J111 = ResultsPostingLineItem::whereRelation('semesterClass.classModel', 'code', 'J111')
        ->whereRelation('header', 'semester_setting_id', $semester_setting_1->id)->first();
    $line_item_J111->update(['total' => 40.00]);

    $line_item_J113 = ResultsPostingLineItem::whereRelation('semesterClass.classModel', 'code', 'J113')
        ->whereRelation('header', 'semester_setting_id', $semester_setting_1->id)->first();
    $line_item_J113->update(['total' => 40.00]);

    $headers = ResultsPostingHeader::select('id')
        ->where([
            'semester_setting_id' => $semester_setting_1->id,
            'report_card_output_code' => 'SEM1RESULT'
        ])
        ->whereIn('grade_id', [$grade_1->id])
        ->get();

    $filters = [
        'grade_ids' => [$grade_1->id ],
        'semester_setting_id' => $semester_setting_1->id,
        'result_posting_header_ids' => $headers
    ];
    $response = $this->service->getNetAveragePassingRateReportData($filters);

    expect($response)->toMatchArray([
        "title" => "2025 Sem 1 J1 Net Average Passing Rate Report",
        "report_data" => [
            "J111" => "50.00",
            "J112" => "75.00",
            "J113" => "50.00"
        ]
    ]);
});


test('getSubjectPassingRateReportData', function (){
    $year = SemesterYearSetting::factory()->create(['year' => '2025']);

    $semester_setting_1 = SemesterSetting::factory()->create(['name' => '2025 Sem 1', 'semester_year_setting_id' => $year->id]);
    $semester_setting_2 = SemesterSetting::factory()->create(['name' => '2025 Sem 2', 'semester_year_setting_id' => $year->id]);


    $grade_1 = Grade::factory()->create(['name->en' => 'J1']);
    $grade_2 = Grade::factory()->create(['name->en' => 'J2']);
    $grade_3 = Grade::factory()->create(['name->en' => 'J3']);

    $subject_1 = Subject::factory()->create(['name->en' => 'Chinese Language']);
    $subject_2 = Subject::factory()->create(['name->en' => 'Mathematics']);

    $grades = Grade::all();

    $exam_1 = Exam::factory()->create(['name->en' => '2025 First Semester Exam' ,'code' => 'SEM1FINAL']);
    $exam_2 = Exam::factory()->create(['name->en' => '2025 Second Semester Exam', 'code' => 'SEM2FINAL']);

    foreach ($grades as $grade){
        $classes = ClassModel::factory(3)->state(new Sequence(
            ['code' => $grade->name.'11', 'name->en' => $grade->name.'11', 'grade_id' => $grade->id,],
            ['code' => $grade->name.'12', 'name->en' => $grade->name.'12','grade_id' => $grade->id],
            ['code' => $grade->name.'13', 'name->en' => $grade->name.'13','grade_id' => $grade->id],
        ))->create();

        $sem1_classes = SemesterClass::factory(3)->state(new Sequence(
            ['semester_setting_id' => $semester_setting_1->id, 'class_id' => $classes[0]->id],
            ['semester_setting_id' => $semester_setting_1->id, 'class_id' => $classes[1]->id],
            ['semester_setting_id' => $semester_setting_1->id, 'class_id' => $classes[2]->id]
        ))->create();

        $sem2_classes = SemesterClass::factory(3)->state(new Sequence(
            ['semester_setting_id' => $semester_setting_2->id, 'class_id' => $classes[0]->id],
            ['semester_setting_id' => $semester_setting_2->id, 'class_id' => $classes[1]->id],
            ['semester_setting_id' => $semester_setting_2->id, 'class_id' => $classes[2]->id]
        ))->create();

        foreach ($sem1_classes as $sem_class){
            $students = Student::factory(5)->create();

            foreach ($students as $student){
                StudentClass::factory()->create([
                    'semester_setting_id' => $semester_setting_1->id,
                    'semester_class_id' => $sem_class->id,
                    'student_id' => $student->id,
                ]);

                $sgf = StudentGradingFramework::factory()->create(['student_id' => $student->id, 'academic_year' => 2025]);

                $result_source = ResultSource::factory()->create(['code' => 'SEM1EXAM', 'student_grading_framework_id' => $sgf->id]);
                ResultSourceExam::factory()->create(['result_source_id' => $result_source->id, 'exam_id' => $exam_1->id]);

                // Subject 1 Marks
                $rss_subject_1 = ResultSourceSubject::factory()->create([
                    'subject_id' => $subject_1->id,
                    'result_source_id' => $result_source->id,
                    'pass_mark' => 60.00
                ]);
                $rss_subject_1_com_1 = ResultSourceSubjectComponent::factory()->create([
                    'result_source_subject_id' => $rss_subject_1->id,
                    'weightage_percent' => 100
                ]);
                $rss_subject_1_com_1 ->update(['actual_score' => 80]);  

                // Subject 2 Marks
                $rss_subject_2 = ResultSourceSubject::factory()->create([
                    'subject_id' => $subject_2->id,
                    'result_source_id' => $result_source->id,
                    'pass_mark' => 60.00
                ]);

                $rss_subject_2_com_1 = ResultSourceSubjectComponent::factory()->create([
                    'result_source_subject_id' => $rss_subject_2->id,
                    'weightage_percent' => 60
                ]);
                $rss_subject_2_com_2 = ResultSourceSubjectComponent::factory()->create([
                    'result_source_subject_id' => $rss_subject_2->id,
                    'weightage_percent' => 40
                ]);

                $rss_subject_2_com_1 ->update(['actual_score' => 80]);
                $rss_subject_2_com_2 ->update(['actual_score' => 70]);
            }

        }

        foreach ($sem2_classes as $sem_class){
            $students = Student::factory(5)->create();

            foreach ($students as $student){
                StudentClass::factory()->create([
                    'semester_setting_id' => $semester_setting_2->id,
                    'semester_class_id' => $sem_class->id,
                    'student_id' => $student->id,
                ]);

                $sgf = StudentGradingFramework::factory()->create(['student_id' => $student->id, 'academic_year' => '2025']);

                $result_source = ResultSource::factory()->create(['code' => 'SEM2EXAM', 'student_grading_framework_id' => $sgf->id]);
                ResultSourceExam::factory()->create(['result_source_id' => $result_source->id, 'exam_id' => $exam_2->id]);

                // Subjet 1 Marks
                $rss_subject_1 = ResultSourceSubject::factory()->create([
                    'subject_id' => $subject_1->id,
                    'result_source_id' => $result_source->id,
                    'pass_mark' => 60.00
                ]);
                $rss_subject_1_com_1 = ResultSourceSubjectComponent::factory()->create([
                    'result_source_subject_id' => $rss_subject_1->id,
                    'weightage_percent' => 100
                ]);
                $rss_subject_1_com_1 ->update(['actual_score' => 80]);  


                // Subject 2 Marks
                $rss_subject_2 = ResultSourceSubject::factory()->create([
                    'subject_id' => $subject_2->id,
                    'result_source_id' => $result_source->id,
                    'pass_mark' => 60.00
                ]);

                $rss_subject_2_com_1 = ResultSourceSubjectComponent::factory()->create([
                    'result_source_subject_id' => $rss_subject_2->id,
                    'weightage_percent' => 60
                ]);
                $rss_subject_2_com_2 = ResultSourceSubjectComponent::factory()->create([
                    'result_source_subject_id' => $rss_subject_2->id,
                    'weightage_percent' => 40
                ]);

                $rss_subject_2_com_1 ->update(['actual_score' => 80]);
                $rss_subject_2_com_2 ->update(['actual_score' => 70]);
            }
        }
    }

    // Expect 100% pass rate on all class (Marks directly from Result Source Subject)
    $filters = [
        'subject_id' => $subject_1->id,
        'grade_id' => $grade_1->id,
        'semester_setting_id' => $semester_setting_1->id,
        'exam_id' => $exam_1->id
    ];

    $response = $this->service->getSubjectPassingRateReportData($filters);
    expect($response)->toMatchArray([
        "title" => "2025 First Semester Exam J1 Chinese Language Passing Rate Report",
        "report_data" => [
          "J111" => "100.00",
          "J112" => "100.00",
          "J113" => "100.00"
        ]
    ]);

    // Expect 100% pass rate on all class (Marks computed from Result Source Subject Component)
    $filters = [
        'subject_id' => $subject_2->id,
        'grade_id' => $grade_1->id,
        'semester_setting_id' => $semester_setting_1->id,
        'exam_id' => $exam_1->id
    ];

    $response = $this->service->getSubjectPassingRateReportData($filters);
    expect($response)->toMatchArray([
        "title" => "2025 First Semester Exam J1 Mathematics Passing Rate Report",
        "report_data" => [
            "J111" => "100.00",
            "J112" => "100.00",
            "J113" => "100.00"
        ]
    ]);

    // Set two sem1 subject 1 marks to 50 (fail) in J111 
    foreach (range(1, 2) as $index){
        $student_ids = StudentClass::select('student_id')
            ->where('semester_setting_id', $semester_setting_1->id)
            ->whereRelation('semesterClass.classModel', 'code', 'J111')
            ->get()
            ->transform(function ($item) {return $item->student_id;})
            ->toArray();

        $component = ResultSourceSubjectComponent::whereRelation('resultSourceSubject.resultSource', 'code', 'SEM1EXAM')
            ->whereHas('resultSourceSubject.resultSource.studentGradingFramework', function ($query) use ($student_ids){
                $query->whereIn('student_id', $student_ids);
            })
            ->whereRelation('resultSourceSubject', 'subject_id', $subject_1->id)
            ->where('actual_score', 80)
            ->first();
        $component->update(['actual_score' => 50.00]);
    }

    // Expect 60% pass for J111 and 100% pass for all others 
    $filters = [
        'subject_id' => $subject_1->id,
        'grade_id' => $grade_1->id,
        'semester_setting_id' => $semester_setting_1->id,
        'exam_id' => $exam_1->id
    ];

    $response = $this->service->getSubjectPassingRateReportData($filters);
    expect($response)->toMatchArray([
        "title" => "2025 First Semester Exam J1 Chinese Language Passing Rate Report",
        "report_data" => [
            "J111" => "60.00",
            "J112" => "100.00",
            "J113" => "100.00"
        ]
    ]);


    // Set two sem1 subject 2 marks to 50 (fail) in J111 
    foreach (range(1, 2) as $index){
        $student_ids = StudentClass::select('student_id')
            ->where('semester_setting_id', $semester_setting_1->id)
            ->whereRelation('semesterClass.classModel', 'code', 'J111')
            ->get()
            ->transform(function ($item) {return $item->student_id;})
            ->toArray();

        $rss = ResultSourceSubject::whereRelation('resultSource', 'code', 'SEM1EXAM')
            ->whereHas('resultSource.studentGradingFramework', function ($query) use ($student_ids) {
                $query->whereIn('student_id', $student_ids);
            })
            ->whereRelation('components', 'actual_score', 70.00)
            ->where(['subject_id' => $subject_2->id])
            ->first();

        $rss->components()->update(['actual_score' => 50.00]);
    }

    // Expect 60% pass for J111 and 100% pass for all others (subject 2, pass rate computed from components)
    $filters = [
        'subject_id' => $subject_2->id,
        'grade_id' => $grade_1->id,
        'semester_setting_id' => $semester_setting_1->id,
        'exam_id' => $exam_1->id
    ];

    $response = $this->service->getSubjectPassingRateReportData($filters);
    expect($response)->toMatchArray([
        "title" => "2025 First Semester Exam J1 Mathematics Passing Rate Report",
        "report_data" => [
          "J111" => "60.00",
          "J112" => "100.00",
          "J113" => "100.00"
        ]
    ]);

    // Expect 100% pass rate for J2 and J3 (unaffected by previous changes)
    $filters = [
        'subject_id' => $subject_1->id,
        'grade_id' => $grade_2->id,
        'semester_setting_id' => $semester_setting_1->id,
        'exam_id' => $exam_1->id
    ];

    $response = $this->service->getSubjectPassingRateReportData($filters);
    expect($response)->toMatchArray([
        "title" => "2025 First Semester Exam J2 Chinese Language Passing Rate Report",
        "report_data" => [
          "J211" => "100.00",
          "J212" => "100.00",
          "J213" => "100.00"
        ]
    ]);

    $filters = [
        'subject_id' => $subject_2->id,
        'grade_id' => $grade_3->id,
        'semester_setting_id' => $semester_setting_1->id,
        'exam_id' => $exam_1->id
    ];

    $response = $this->service->getSubjectPassingRateReportData($filters);
    expect($response)->toMatchArray([
        "title" => "2025 First Semester Exam J3 Mathematics Passing Rate Report",
        "report_data" => [
          "J311" => "100.00",
          "J312" => "100.00",
          "J313" => "100.00"
        ]
    ]);

    // Expect 100% pass rate for J1 SEM2 (unaffected by previous changes)
    $filters = [
        'subject_id' => $subject_1->id,
        'grade_id' => $grade_1->id,
        'semester_setting_id' => $semester_setting_2->id,
        'exam_id' => $exam_2->id
    ];

    $response = $this->service->getSubjectPassingRateReportData($filters);
    expect($response)->toMatchArray([
        "title" => "2025 Second Semester Exam J1 Chinese Language Passing Rate Report",
        "report_data" => [
          "J111" => "100.00",
          "J112" => "100.00",
          "J113" => "100.00"
        ]
    ]);

    $filters = [
        'subject_id' => $subject_2->id,
        'grade_id' => $grade_1->id,
        'semester_setting_id' => $semester_setting_2->id,
        'exam_id' => $exam_2->id
    ];

    $response = $this->service->getSubjectPassingRateReportData($filters);
    expect($response)->toMatchArray([
        "title" => "2025 Second Semester Exam J1 Mathematics Passing Rate Report",
        "report_data" => [
          "J111" => "100.00",
          "J112" => "100.00",
          "J113" => "100.00"
        ]
    ]);

});

test('getSubjectAverageMarkReportData', function (){
    $year = SemesterYearSetting::factory()->create(['year' => '2025']);

    $semester_setting_1 = SemesterSetting::factory()->create(['semester_year_setting_id' => $year->id]);
    $semester_setting_2 = SemesterSetting::factory()->create(['semester_year_setting_id' => $year->id]);


    $grade_1 = Grade::factory()->create(['name->en' => 'J1']);
    $grade_2 = Grade::factory()->create(['name->en' => 'J2']);
    $grade_3 = Grade::factory()->create(['name->en' => 'J3']);

    $subject_1 = Subject::factory()->create(['name->en' => 'Chinese Language']);
    $subject_2 = Subject::factory()->create(['name->en' => 'Mathematics']);

    $grades = Grade::all();

    $exam_1 = Exam::factory()->create(['name->en' => '2025 First Semester Exam' ,'code' => 'SEM1FINAL']);
    $exam_2 = Exam::factory()->create(['name->en' => '2025 Second Semester Exam', 'code' => 'SEM2FINAL']);

    foreach ($grades as $grade){
        $classes = ClassModel::factory(3)->state(new Sequence(
            ['code' => $grade->name.'11', 'name->en' => $grade->name.'11', 'grade_id' => $grade->id,],
            ['code' => $grade->name.'12', 'name->en' => $grade->name.'12','grade_id' => $grade->id],
            ['code' => $grade->name.'13', 'name->en' => $grade->name.'13','grade_id' => $grade->id],
        ))->create();

        $sem1_classes = SemesterClass::factory(3)->state(new Sequence(
            ['semester_setting_id' => $semester_setting_1->id, 'class_id' => $classes[0]->id],
            ['semester_setting_id' => $semester_setting_1->id, 'class_id' => $classes[1]->id],
            ['semester_setting_id' => $semester_setting_1->id, 'class_id' => $classes[2]->id]
        ))->create();

        $sem2_classes = SemesterClass::factory(3)->state(new Sequence(
            ['semester_setting_id' => $semester_setting_2->id, 'class_id' => $classes[0]->id],
            ['semester_setting_id' => $semester_setting_2->id, 'class_id' => $classes[1]->id],
            ['semester_setting_id' => $semester_setting_2->id, 'class_id' => $classes[2]->id]
        ))->create();

        foreach ($sem1_classes as $sem_class){
            $students = Student::factory(5)->create();

            foreach ($students as $key => $student){
                StudentClass::factory()->create([
                    'semester_setting_id' => $semester_setting_1->id,
                    'semester_class_id' => $sem_class->id,
                    'student_id' => $student->id,
                ]);

                $sgf = StudentGradingFramework::factory()->create(['student_id' => $student->id, 'academic_year' => 2025]);

                $result_source = ResultSource::factory()->create(['code' => 'SEM1EXAM', 'student_grading_framework_id' => $sgf->id]);

                ResultSourceExam::factory()->create(['result_source_id' => $result_source->id, 'exam_id' => $exam_1->id]);

                $rss_subject_1 = ResultSourceSubject::factory()->create([
                    'subject_id' => $subject_1->id,
                    'result_source_id' => $result_source->id,
                    'pass_mark' => 60.00
                ]);

                $rss_subject_1_com_1 = ResultSourceSubjectComponent::factory()->create([
                    'result_source_subject_id' => $rss_subject_1->id,
                    'weightage_percent' => 100
                ]);
                // this will sum up to an average of 75 (65, 70, 75, 80, 85);
                $rss_subject_1_com_1 ->update(['actual_score' => bcadd(65, $key * 5, 2)]);  

                $rss_subject_2 = ResultSourceSubject::factory()->create([
                    'subject_id' => $subject_2->id,
                    'result_source_id' => $result_source->id,
                    'pass_mark' => 60.00
                ]);

                $rss_subject_2_com_1 = ResultSourceSubjectComponent::factory()->create([
                    'result_source_subject_id' => $rss_subject_2->id,
                    'weightage_percent' => 60
                ]);
                $rss_subject_2_com_2 = ResultSourceSubjectComponent::factory()->create([
                    'result_source_subject_id' => $rss_subject_2->id,
                    'weightage_percent' => 40
                ]);

                // after weightage multiplied totals : 61, 66, 71, 76, 81
                $rss_subject_2_com_1 ->update(['actual_score' => bcadd(65, $key * 5, 2)]);
                $rss_subject_2_com_2 ->update(['actual_score' => bcadd(55, $key * 5, 2)]);
            }

        }

        foreach ($sem2_classes as $sem_class){
            $students = Student::factory(5)->create();

            foreach ($students as $key => $student){
                StudentClass::factory()->create([
                    'semester_setting_id' => $semester_setting_2->id,
                    'semester_class_id' => $sem_class->id,
                    'student_id' => $student->id,
                ]);

                $sgf = StudentGradingFramework::factory()->create(['student_id' => $student->id, 'academic_year' => '2025']);

                $result_source = ResultSource::factory()->create(['code' => 'SEM2EXAM', 'student_grading_framework_id' => $sgf->id]);
                ResultSourceExam::factory()->create(['result_source_id' => $result_source->id, 'exam_id' => $exam_2->id]);

                $rss_subject_1 = ResultSourceSubject::factory()->create([
                    'subject_id' => $subject_1->id,
                    'result_source_id' => $result_source->id,
                    'pass_mark' => 60.00
                ]);
                $rss_subject_1_com_1 = ResultSourceSubjectComponent::factory()->create([
                    'result_source_subject_id' => $rss_subject_1->id,
                    'weightage_percent' => 100
                ]);
                // this will sum up to an average of 75 (65, 70, 75, 80, 85);
                $rss_subject_1_com_1 ->update(['actual_score' => bcadd(65, $key * 5, 2)]); 

                $rss_subject_2 = ResultSourceSubject::factory()->create([
                    'subject_id' => $subject_2->id,
                    'result_source_id' => $result_source->id,
                    'pass_mark' => 60.00
                ]);

                $rss_subject_2_com_1 = ResultSourceSubjectComponent::factory()->create([
                    'result_source_subject_id' => $rss_subject_2->id,
                    'weightage_percent' => 60
                ]);
                $rss_subject_2_com_2 = ResultSourceSubjectComponent::factory()->create([
                    'result_source_subject_id' => $rss_subject_2->id,
                    'weightage_percent' => 40
                ]);

                $rss_subject_2_com_1 ->update(['actual_score' => bcadd(65, $key * 5, 2)]);
                $rss_subject_2_com_2 ->update(['actual_score' => bcadd(55, $key * 5, 2)]);
            }
        }
    }

    // expect average marks to all be 75 (testing subject 1 - evaluate by direct score)
    $filters = [
        'subject_id' => $subject_1->id,
        'grade_id' => $grade_1->id,
        'semester_setting_id' => $semester_setting_1->id,
        'exam_id' => $exam_1->id
    ];

    $response = $this->service->getSubjectAverageMarkReportData($filters);
    expect($response)->toMatchArray([
        "title" => "2025 First Semester Exam J1 Chinese Language Average Mark Report",
        "report_data" => [
          "J111" => "75.00",
          "J112" => "75.00",
          "J113" => "75.00"
        ]
    ]);


     // expect average marks to all be 71 (testing subject 2 - evaluate by component)
    $filters = [
        'subject_id' => $subject_2->id,
        'grade_id' => $grade_1->id,
        'semester_setting_id' => $semester_setting_1->id,
        'exam_id' => $exam_1->id
    ];

    $response = $this->service->getSubjectAverageMarkReportData($filters);
    expect($response)->toMatchArray([
        "title" => "2025 First Semester Exam J1 Mathematics Average Mark Report",
        "report_data" => [
          "J111" => "71.00",
          "J112" => "71.00",
          "J113" => "71.00"
        ]
    ]);

    // Set two sem1 subject 1 marks to 50 in J111, lowering average to 68
    $student_ids = StudentClass::select('student_id')
        ->where('semester_setting_id', $semester_setting_1->id)
        ->whereRelation('semesterClass.classModel', 'code', 'J111')
        ->get()
        ->transform(function ($item) {return $item->student_id;})
        ->toArray();

    $rss = ResultSourceSubjectComponent::whereRelation('resultSourceSubject.resultSource', 'code', 'SEM1EXAM')
        ->whereHas('resultSourceSubject.resultSource.studentGradingFramework', function ($query) use ($student_ids) {
            $query->whereIn('student_id', $student_ids);
        })
        ->whereRelation('resultSourceSubject', 'subject_id', $subject_1->id)
        ->where(['actual_score' => 65])
        ->first();
    $rss->update(['actual_score' => 50.00]);

    $rss = ResultSourceSubjectComponent::whereRelation('resultSourceSubject.resultSource', 'code', 'SEM1EXAM')
        ->whereHas('resultSourceSubject.resultSource.studentGradingFramework', function ($query) use ($student_ids) {
            $query->whereIn('student_id', $student_ids);
        })
        ->whereRelation('resultSourceSubject', 'subject_id', $subject_1->id)
        ->where(['actual_score' => 70])
        ->first();
    $rss->update(['actual_score' => 50.00]);

    $filters = [
        'subject_id' => $subject_1->id,
        'grade_id' => $grade_1->id,
        'semester_setting_id' => $semester_setting_1->id,
        'exam_id' => $exam_1->id
    ];

    $response = $this->service->getSubjectAverageMarkReportData($filters);
    expect($response)->toMatchArray([
        "title" => "2025 First Semester Exam J1 Chinese Language Average Mark Report",
        "report_data" => [
          "J111" => "68.00",
          "J112" => "75.00",
          "J113" => "75.00"
        ]
    ]);

    // Lower the average of two subject components in subject 2 to 68.80
    $student_ids = StudentClass::select('student_id')
        ->where('semester_setting_id', $semester_setting_1->id)
        ->whereRelation('semesterClass.classModel', 'code', 'J111')
        ->get()
        ->transform(function ($item) {return $item->student_id;})
        ->toArray();

    $rss = ResultSourceSubject::whereRelation('resultSource', 'code', 'SEM1EXAM')
        ->whereHas('resultSource.studentGradingFramework', function ($query) use ($student_ids) {
            $query->whereIn('student_id', $student_ids);
        })
        ->whereRelation('components', 'actual_score', 65.00)
        ->where(['subject_id' => $subject_2->id])
        ->first();

    $rss->components()->update(['actual_score' => 50.00]);


    $filters = [
        'subject_id' => $subject_2->id,
        'grade_id' => $grade_1->id,
        'semester_setting_id' => $semester_setting_1->id,
        'exam_id' => $exam_1->id
    ];

    $response = $this->service->getSubjectAverageMarkReportData($filters);
    expect($response)->toMatchArray([
        "title" => "2025 First Semester Exam J1 Mathematics Average Mark Report",
        "report_data" => [
          "J111" => "68.80",
          "J112" => "71.00",
          "J113" => "71.00"
        ]
    ]);

    // test J2 and J3 - mix/match grade, semester setting (should be unaffected by previous change)
    $filters = [
        'subject_id' => $subject_1->id,
        'grade_id' => $grade_2->id,
        'semester_setting_id' => $semester_setting_2->id,
        'exam_id' => $exam_2->id
    ];

    $response = $this->service->getSubjectAverageMarkReportData($filters);
    expect($response)->toMatchArray([
        "title" => "2025 Second Semester Exam J2 Chinese Language Average Mark Report",
        "report_data" => [
          "J211" => "75.00",
          "J212" => "75.00",
          "J213" => "75.00"
        ]
    ]);

    $filters = [
        'subject_id' => $subject_2->id,
        'grade_id' => $grade_3->id,
        'semester_setting_id' => $semester_setting_1->id,
        'exam_id' => $exam_1->id
    ];

    $response = $this->service->getSubjectAverageMarkReportData($filters);
    expect($response)->toMatchArray([
        "title" => "2025 First Semester Exam J3 Mathematics Average Mark Report",
        "report_data" =>  [
          "J311" => "71.00",
          "J312" => "71.00",
          "J313" => "71.00"
        ]
    ]);
});

test('getSubjectScoreAnalysisReportData', function(){
    $year = SemesterYearSetting::factory()->create(['year' => '2025']);

    $semester_setting_1 = SemesterSetting::factory()->create(['semester_year_setting_id' => $year->id]);
    $semester_setting_2 = SemesterSetting::factory()->create(['semester_year_setting_id' => $year->id]);


    $subjects = Subject::factory(3)->state(new Sequence (
        [ 
            'name->en' => 'Chinese Language',
            'name->zh' => '华文'
        ],
        [
            'name->en' => 'Mathematics',
            'name->zh' => '数学'
        ],
        [
            'name->en' => 'History',
            'name->zh' => '历史'
        ]
    ))->create();


    $grade_1 = Grade::factory()->create(['name->en' => 'J1']);

    $class_1 = ClassModel::factory()->create(['name->en' => 'J111', 'code' => 'J111', 'grade_id' => $grade_1->id]);
    $class_2 = ClassModel::factory()->create(['name->en' => 'J111', 'code' => 'J112', 'grade_id' => $grade_1->id]);

    $sem_class_1 = SemesterClass::factory()->create(['semester_setting_id' => $semester_setting_1->id, 'class_id' => $class_1->id]);
    $sem_class_2 = SemesterClass::factory()->create(['semester_setting_id' => $semester_setting_1->id, 'class_id' => $class_2->id]);

    $exam_1 = Exam::factory()->create(['name->en' => '2025 First Semester Exam' ,'code' => 'SEM1FINAL']);
    $exam_2 = Exam::factory()->create(['name->en' => '2025 Second Semester Exam', 'code' => 'SEM2FINAL']);

    // Creating Students for Sem Class J111
    $students_in_class_1 = Student::factory(5)->create();
    foreach ($students_in_class_1 as $key => $student){
        StudentClass::factory()->create([
            'semester_setting_id' => $semester_setting_1->id,
            'semester_class_id' => $sem_class_1->id,
            'student_id' => $student->id
        ]);

        $sgf = StudentGradingFramework::factory()->create(['student_id' => $student->id, 'academic_year' => '2025']);
        $result_source = ResultSource::factory()->create(['code' => 'SEM1EXAM', 'student_grading_framework_id' => $sgf->id]);

        ResultSourceExam::factory()->create(['result_source_id' => $result_source->id, 'exam_id' => $exam_1->id]);

        // Subject 0 marks
        $rss_subject_0 = ResultSourceSubject::factory()->create([
            'subject_id' => $subjects[0]->id,
            'result_source_id' => $result_source->id,
        ]);
        $rss_subject_0_com_1 = ResultSourceSubjectComponent::factory()->create([
            'result_source_subject_id' => $rss_subject_0->id,
            'weightage_percent' => 100
        ]);
        $rss_subject_0_com_1 ->update(['actual_score' => 30 + $key * 10]);

        // Subject 1 Marks
        $rss_subject_1 = ResultSourceSubject::factory()->create([
            'subject_id' => $subjects[1]->id,
            'result_source_id' => $result_source->id,
            'pass_mark' => 60.00
        ]);

        $rss_subject_1_com_1 = ResultSourceSubjectComponent::factory()->create([
            'result_source_subject_id' => $rss_subject_1->id,
            'weightage_percent' => 60
        ]);
        $rss_subject_1_com_2 = ResultSourceSubjectComponent::factory()->create([
            'result_source_subject_id' => $rss_subject_1->id,
            'weightage_percent' => 40
        ]);

        $rss_subject_1_com_1 ->update(['actual_score' => 35 + $key * 10]);
        $rss_subject_1_com_2 ->update(['actual_score' => 35 + $key * 10]);

        // Subject 2 Marks
        $rss_subject_2 = ResultSourceSubject::factory()->create([
            'subject_id' => $subjects[2]->id,
            'result_source_id' => $result_source->id,
            'pass_mark' => 60.00
        ]);

        $rss_subject_2_com_1 = ResultSourceSubjectComponent::factory()->create([
            'result_source_subject_id' => $rss_subject_2->id,
            'weightage_percent' => 100
        ]);
        $rss_subject_2_com_1 ->update(['actual_score' => 47 + $key * 10]);

    }

    // Creating Students for Sem Class J112
    $students_in_class_2 = Student::factory(5)->create();
    foreach ($students_in_class_2 as $key => $student){
        StudentClass::factory()->create([
            'semester_setting_id' => $semester_setting_2->id,
            'semester_class_id' => $sem_class_2->id,
            'student_id' => $student->id
        ]);

        $sgf = StudentGradingFramework::factory()->create(['student_id' => $student->id, 'academic_year' => '2025']);
        $result_source = ResultSource::factory()->create(['code' => 'SEM2EXAM', 'student_grading_framework_id' => $sgf->id]);

        ResultSourceExam::factory()->create(['result_source_id' => $result_source->id, 'exam_id' => $exam_2->id]);

        // Subject 0 Marks
        $rss_subject_0 = ResultSourceSubject::factory()->create([
            'subject_id' => $subjects[0]->id,
            'result_source_id' => $result_source->id,
        ]);
        $rss_subject_0_com_1 = ResultSourceSubjectComponent::factory()->create([
            'result_source_subject_id' => $rss_subject_0->id,
            'weightage_percent' => 100
        ]);
        $rss_subject_0_com_1 ->update(['actual_score' => 20 + $key * 5]);

        // Subject 1 Marks
        $rss_subject_1 = ResultSourceSubject::factory()->create([
            'subject_id' => $subjects[1]->id,
            'result_source_id' => $result_source->id,
            'pass_mark' => 60.00
        ]);

        $rss_subject_1_com_1 = ResultSourceSubjectComponent::factory()->create([
            'result_source_subject_id' => $rss_subject_1->id,
            'weightage_percent' => 60
        ]);
        $rss_subject_1_com_2 = ResultSourceSubjectComponent::factory()->create([
            'result_source_subject_id' => $rss_subject_1->id,
            'weightage_percent' => 40
        ]);

        $rss_subject_1_com_1 ->update(['actual_score' => 25 + $key * 5]);
        $rss_subject_1_com_2 ->update(['actual_score' => 25 + $key * 5]);

        // Subject 2 Marks
        $rss_subject_2 = ResultSourceSubject::factory()->create([
            'subject_id' => $subjects[2]->id,
            'result_source_id' => $result_source->id,
            'pass_mark' => 60.00
        ]);

        $rss_subject_2_com_1 = ResultSourceSubjectComponent::factory()->create([
            'result_source_subject_id' => $rss_subject_2->id,
            'weightage_percent' => 100
        ]);
        $rss_subject_2_com_1 ->update(['actual_score' => 40 + $key * 5]);
    }
    
    // Testing SEM1 Class
    $filters = [
        'semester_class_id' => $sem_class_1->id,
        'semester_setting_id' => $semester_setting_1->id,
        'exam_id' => $exam_1->id
    ];

    $response = $this->service->getSubjectScoreAnalysisReportData($filters);
    expect($response['title'])->toBe('2025 First Semester Exam Analysis of Examination Results for J111')
        ->and($response['report_data'])->toHaveCount(3)
        ->and($response['report_data'][$subjects[0]->name])->toMatchArray([
            "0-9.9" => 0,
            "10-19.99" => 0,
            "20-29.99" => 0,
            "30-39.99" => 1,
            "40-49.99" => 1,
            "50-59.99" => 1,
            "60-69.99" => 1,
            "70-79.99" => 1,
            "80-89.99" => 0,
            "90-100" => 0,
        ])
        ->and($response['report_data'][$subjects[1]->name])->toMatchArray([
            "0-9.9" => 0,
            "10-19.99" => 0,
            "20-29.99" => 0,
            "30-39.99" => 1,
            "40-49.99" => 1,
            "50-59.99" => 1,
            "60-69.99" => 1,
            "70-79.99" => 1,
            "80-89.99" => 0,
            "90-100" => 0,
        ])
        ->and($response['report_data'][$subjects[2]->name])->toMatchArray([
            "0-9.9" => 0,
            "10-19.99" => 0,
            "20-29.99" => 0,
            "30-39.99" => 0,
            "40-49.99" => 1,
            "50-59.99" => 1,
            "60-69.99" => 1,
            "70-79.99" => 1,
            "80-89.99" => 1,
            "90-100" => 0,
        ]);

    // Testing SEM2 Class
    $filters = [
        'semester_class_id' => $sem_class_2->id,
        'semester_setting_id' => $semester_setting_2->id,
        'exam_id' => $exam_2->id
    ];

    $response = $this->service->getSubjectScoreAnalysisReportData($filters);

    expect($response['title'])->toBe('2025 Second Semester Exam Analysis of Examination Results for J111')
        ->and($response['report_data'])->toHaveCount(3)
        ->and($response['report_data'][$subjects[0]->name])->toMatchArray([
            "0-9.9" => 0,
            "10-19.99" => 0,
            "20-29.99" => 2,
            "30-39.99" => 2,
            "40-49.99" => 1,
            "50-59.99" => 0,
            "60-69.99" => 0,
            "70-79.99" => 0,
            "80-89.99" => 0,
            "90-100" => 0,
        ])
        ->and($response['report_data'][$subjects[1]->name])->toMatchArray([
            "0-9.9" => 0,
            "10-19.99" => 0,
            "20-29.99" => 1,
            "30-39.99" => 2,
            "40-49.99" => 2,
            "50-59.99" => 0,
            "60-69.99" => 0,
            "70-79.99" => 0,
            "80-89.99" => 0,
            "90-100" => 0,
        ])
        ->and($response['report_data'][$subjects[2]->name])->toMatchArray([
            "0-9.9" => 0,
            "10-19.99" => 0,
            "20-29.99" => 0,
            "30-39.99" => 0,
            "40-49.99" => 2,
            "50-59.99" => 2,
            "60-69.99" => 1,
            "70-79.99" => 0,
            "80-89.99" => 0,
            "90-100" => 0,
        ]);
    
    // Update SEM1 marks for subject 0, changes should be reflected
    ResultSourceSubjectComponent::whereRelation('resultSourceSubject', 'subject_id', $subjects[0]->id)
        ->whereRelation('resultSourceSubject.resultSource.studentGradingFramework', 'student_id', $students_in_class_1->first()->id)
        ->update(['actual_score' => 100]);

    $filters = [
        'semester_class_id' => $sem_class_1->id,
        'semester_setting_id' => $semester_setting_1->id,
        'exam_id' => $exam_1->id
    ];

    $response = $this->service->getSubjectScoreAnalysisReportData($filters);

    expect($response['title'])->toBe('2025 First Semester Exam Analysis of Examination Results for J111')
        ->and($response['report_data'])->toHaveCount(3)
        ->and($response['report_data'][$subjects[0]->name])->toMatchArray([
            "0-9.9" => 0,
            "10-19.99" => 0,
            "20-29.99" => 0,
            "30-39.99" => 0,
            "40-49.99" => 1,
            "50-59.99" => 1,
            "60-69.99" => 1,
            "70-79.99" => 1,
            "80-89.99" => 0,
            "90-100" => 1,
        ])
        ->and($response['report_data'][$subjects[1]->name])->toMatchArray([
            "0-9.9" => 0,
            "10-19.99" => 0,
            "20-29.99" => 0,
            "30-39.99" => 1,
            "40-49.99" => 1,
            "50-59.99" => 1,
            "60-69.99" => 1,
            "70-79.99" => 1,
            "80-89.99" => 0,
            "90-100" => 0,
        ])
        ->and($response['report_data'][$subjects[2]->name])->toMatchArray([
            "0-9.9" => 0,
            "10-19.99" => 0,
            "20-29.99" => 0,
            "30-39.99" => 0,
            "40-49.99" => 1,
            "50-59.99" => 1,
            "60-69.99" => 1,
            "70-79.99" => 1,
            "80-89.99" => 1,
            "90-100" => 0,
        ]);

    // Update one of subject 0 component to null - should have only 4 students
    ResultSourceSubjectComponent::whereRelation('resultSourceSubject', 'subject_id', $subjects[0]->id)
        ->whereRelation('resultSourceSubject.resultSource.studentGradingFramework', 'student_id', $students_in_class_1->first()->id)
        ->update(['actual_score' => null]);

    $filters = [
        'semester_class_id' => $sem_class_1->id,
        'semester_setting_id' => $semester_setting_1->id,
        'exam_id' => $exam_1->id
    ];

    $response = $this->service->getSubjectScoreAnalysisReportData($filters);

    expect($response['title'])->toBe('2025 First Semester Exam Analysis of Examination Results for J111')
        ->and($response['report_data'])->toHaveCount(3)
        ->and($response['report_data'][$subjects[0]->name])->toMatchArray([
            "0-9.9" => 0,
            "10-19.99" => 0,
            "20-29.99" => 0,
            "30-39.99" => 0,
            "40-49.99" => 1,
            "50-59.99" => 1,
            "60-69.99" => 1,
            "70-79.99" => 1,
            "80-89.99" => 0,
            "90-100" => 0,
        ])
        ->and($response['report_data'][$subjects[1]->name])->toMatchArray([
            "0-9.9" => 0,
            "10-19.99" => 0,
            "20-29.99" => 0,
            "30-39.99" => 1,
            "40-49.99" => 1,
            "50-59.99" => 1,
            "60-69.99" => 1,
            "70-79.99" => 1,
            "80-89.99" => 0,
            "90-100" => 0,
        ])
        ->and($response['report_data'][$subjects[2]->name])->toMatchArray([
            "0-9.9" => 0,
            "10-19.99" => 0,
            "20-29.99" => 0,
            "30-39.99" => 0,
            "40-49.99" => 1,
            "50-59.99" => 1,
            "60-69.99" => 1,
            "70-79.99" => 1,
            "80-89.99" => 1,
            "90-100" => 0,
        ]);
    

    // Update all subject 0 to null - subject should no longer appear
    ResultSourceSubjectComponent::whereRelation('resultSourceSubject', 'subject_id', $subjects[0]->id)
        ->update(['actual_score' => null]);

    $filters = [
        'semester_class_id' => $sem_class_1->id,
        'semester_setting_id' => $semester_setting_1->id,
        'exam_id' => $exam_1->id
    ];

    $response = $this->service->getSubjectScoreAnalysisReportData($filters);

    expect($response['title'])->toBe('2025 First Semester Exam Analysis of Examination Results for J111')
        ->and($response['report_data'])->toHaveCount(2)
        ->and($response['report_data'][$subjects[1]->name])->toMatchArray([
            "0-9.9" => 0,
            "10-19.99" => 0,
            "20-29.99" => 0,
            "30-39.99" => 1,
            "40-49.99" => 1,
            "50-59.99" => 1,
            "60-69.99" => 1,
            "70-79.99" => 1,
            "80-89.99" => 0,
            "90-100" => 0,
        ])
        ->and($response['report_data'][$subjects[2]->name])->toMatchArray([
            "0-9.9" => 0,
            "10-19.99" => 0,
            "20-29.99" => 0,
            "30-39.99" => 0,
            "40-49.99" => 1,
            "50-59.99" => 1,
            "60-69.99" => 1,
            "70-79.99" => 1,
            "80-89.99" => 1,
            "90-100" => 0,
        ]);
});
