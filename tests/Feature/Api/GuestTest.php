<?php

use App\Enums\GuestType;
use App\Http\Resources\UserResource;
use App\Models\Currency;
use App\Models\Guest;
use App\Models\User;
use App\Services\GuestService;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PermissionSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Hash;
use Laravel\Sanctum\Sanctum;
use Mockery\MockInterface;

beforeEach(function () {
    $this->seed([
        InternationalizationSeeder::class,
        PermissionSeeder::class,
    ]);

    app()->setLocale('en');

    $this->withHeaders([
        'Accept-Language' => 'en'
    ]);

    $user = User::factory()->create([
        'password' => Hash::make('123456'),
    ]);

    $user->assignRole('Super Admin');

    Sanctum::actingAs($user);

    $this->table = resolve(Guest::class)->getTable();
    $this->routeNamePrefix = 'guests';

    $this->currency = Currency::factory()->malaysiaCurrency()->create();
});

test('index', function () {

    $guests = Guest::factory(3)->state(new Sequence(
        [
            'user_id' => User::factory(),
            'name->en' => 'Guest 1',
            'name->zh' => '客人',
            'email' => fake()->unique()->safeEmail(),
            'phone_number' => '+60123456789',
            'type' => GuestType::ALUMNI->value,
            'nric' => fake('ms_MY')->myKadNumber(),
            'remarks' => 'This is first guest'
        ],
        [
            'user_id' => User::factory(),
            'name->en' => 'A Guest 2',
            'name->zh' => '客人2',
            'email' => fake()->unique()->safeEmail(),
            'phone_number' => '+60123456790',
            'type' => GuestType::ALUMNI->value,
            'nric' => fake('ms_MY')->myKadNumber(),
            'remarks' => 'second guest'
        ],
        [
            'user_id' => User::factory(),
            'name->en' => 'B Guest 3',
            'name->zh' => '客人3',
            'email' => fake()->unique()->safeEmail(),
            'phone_number' => '+60123456791',
            'type' => GuestType::COMMITTEE->value,
            'nric' => fake('ms_MY')->myKadNumber(),
            'remarks' => 'The 3rd guest'
        ]
    ))->create();

    $filters = [
        'name' => 'A Guest 2',
        'type' => GuestType::ALUMNI->value,
        'phone_number' => '************'
    ];

    $this->mock(GuestService::class, function (MockInterface $mock) use ($filters, $guests) {
        $mock->shouldReceive('getAllPaginatedGuests')->with($filters)->once()->andReturn(new LengthAwarePaginator([$guests[2]], 1, 1));
    });

    $response = $this->getJson(route($this->routeNamePrefix . '.index', $filters))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->and($response['data'][0])->toMatchArray([
            'name' => $guests[2]->name,
            'email' => $guests[2]->email,
            'phone_number' => $guests[2]->phone_number,
            'type' => $guests[2]->type->value,
            'nric' => $guests[2]->nric,
            'remarks' => $guests[2]->remarks
        ]);
});

test('show success', function () {
    $guest_user = User::factory()->create();
    $guest = Guest::factory()->create([
        'user_id' => $guest_user->id,
        'name->en' => 'Guest 1',
        'name->zh' => '客人',
        'email' => fake()->unique()->safeEmail(),
        'phone_number' => '+60123456789',
        'type' => GuestType::ALUMNI->value,
        'nric' => fake('ms_MY')->myKadNumber(),
        'remarks' => 'This is first guest'
    ]);
    Guest::factory(3)->create();

    //test with id exist
    $response = $this->getJson(route("$this->routeNamePrefix.show", ['guest' => $guest->id]))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toEqual([
            'id' => $guest->id,
            'name' => $guest->name,
            'email' => $guest->email,
            'phone_number' => $guest->phone_number,
            'type' => $guest->type->value,
            'nric' => $guest->nric,
            'remarks' => $guest->remarks,
            'user' => resourceToArray(new UserResource($guest_user)),
            'translations' => $guest->translations
        ]);
});

test('show fail - id does not exist', function () {
    $guest = Guest::factory()->create([
        'user_id' => User::factory(),
        'name->en' => 'Guest 1',
        'name->zh' => '客人',
        'email' => fake()->unique()->safeEmail(),
        'phone_number' => '+60123456789',
        'type' => GuestType::ALUMNI->value,
        'nric' => fake('ms_MY')->myKadNumber(),
        'remarks' => 'This is first guest'
    ]);
    Guest::factory(3)->create();

    $response = $this->getJson(route("$this->routeNamePrefix.show", ['guest' => 9999]))->json();

    expect($response)->toHaveModelResourceNotFoundResponse();
});

test('store', function () {
    //store success
    $this->assertDatabaseCount($this->table, 0);
    $this->assertDatabaseCount('users', 1);

    $payload = [
        'name' => [
            'en' => 'Guest 1',
            'zh' => '客人',
        ],
        'email' => fake()->unique()->safeEmail(),
        'phone_number' => '+60123456789',
        'type' => GuestType::ALUMNI->value,
        'nric' => fake('ms_MY')->myKadNumber(),
        'remarks' => 'This is first guest'
    ];

    $response = $this->postJson(route("$this->routeNamePrefix.create"), $payload)->json();
    $guest = Guest::find($response['data']['id']);
    $guest_user = $guest->user;

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'name' => $payload['name']['en'],
            'email' => $payload['email'],
            'phone_number' => $payload['phone_number'],
            'nric' => $payload['nric'],
            'type' => $payload['type'],
            'remarks' => $payload['remarks'],
            'user' => resourceToArray(new UserResource($guest_user))
        ]);

    $guest = Guest::find($response['data']['id']);
    $guest_user = $guest->user;

    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseCount('users', 2);

    $this->assertDatabaseHas($this->table, [
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
        'email' => $payload['email'],
        'phone_number' => $payload['phone_number'],
        'nric' => $payload['nric'],
        'type' => $payload['type'],
        'remarks' => $payload['remarks']
    ]);

    $this->assertDatabaseHas('users', [
        'id' => $guest_user->id,
        'email' => $guest_user->email,
        'phone_number' => $guest_user->phone_number,
    ]);
});

test('store validation', function () {
    $guest_user = User::factory()->create([
        'email' => '<EMAIL>',
        'phone_number' => '************'
    ]);

    Guest::factory()->create([
        'user_id' => $guest_user->id,
        'name->en' => 'Guest 1',
        'name->zh' => '客人',
        'email' => '<EMAIL>',
        'phone_number' => '+60123456789',
        'type' => GuestType::ALUMNI->value,
        'nric' => '876543-12-1280',
        'remarks' => 'This is first guest'
    ]);

    $this->assertDatabaseCount($this->table, 1);

    $payload = [
        'name' => [
            'en' => 'Guest 1',
            'zh' => '客人',
        ],
        'email' => '<EMAIL>',
        'phone_number' => '+60123456789',
        'type' => 'NONEXIST',
        'nric' => '876543-12-1280',
        'remarks' => 'This is first guest'
    ];

    $response = $this->postJson(route("$this->routeNamePrefix.create"), $payload)->json();

    $this->assertDatabaseCount($this->table, 1);
    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['status'])->toBe('ERROR')
        ->and($response['code'])->toBe(422)
        ->and($response['error'])->toBe([
            'email' => [
                'The email has already been taken.'
            ],
            'phone_number' => [
                'The phone number has already been taken.'
            ],
            'nric' => [
                'The nric has already been taken.',
                'The nric field must be a number.',
                'The nric field must be 12 digits.'
            ],
            'type' => [
                'The selected type is invalid.'
            ]
        ]);
});

test('update', function () {
    $guest = Guest::factory()->create([
        'user_id' => User::factory(),
        'name->en' => 'Guest 1',
        'name->zh' => '客人',
        'email' => fake()->unique()->safeEmail(),
        'phone_number' => '+60123456789',
        'type' => GuestType::ALUMNI->value,
        'nric' => fake('ms_MY')->myKadNumber(),
        'remarks' => 'This is first guest'
    ]);

    Guest::factory(3)->create();

    $this->assertDatabaseCount($this->table, 4);

    $payload = [
        'name' => [
            'en' => 'New Guest 1',
            'zh' => '客人123',
        ],
        'email' => '<EMAIL>',
        'phone_number' => '+60123456790',
        'type' => GuestType::COMMITTEE->value,
        'nric' => fake('ms_MY')->myKadNumber(),
        'remarks' => 'Updated guest'
    ];

    $response = $this->putJson((route("$this->routeNamePrefix.update", ['guest' => $guest->id])), $payload)->json();

    $guest = Guest::find($response['data']['id']);
    $guest_user = $guest->user;

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'name' => $payload['name']['en'],
            'email' => $payload['email'],
            'phone_number' => $payload['phone_number'],
            'nric' => $payload['nric'],
            'type' => $payload['type'],
            'remarks' => $payload['remarks'],
            'user' => resourceToArray(new UserResource($guest_user))
        ]);

    $this->assertDatabaseCount($this->table, 4);
    $this->assertDatabaseHas($this->table, [
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
        'email' => $payload['email'],
        'phone_number' => $payload['phone_number'],
        'nric' => $payload['nric'],
        'type' => $payload['type'],
        'remarks' => $payload['remarks']
    ]);
});

test('update validation', function () {
    Guest::factory()->create([
        'user_id' => User::factory()->create(),
        'name->en' => 'Guest 1',
        'name->zh' => '客人',
        'email' => '<EMAIL>',
        'phone_number' => '+60123456789',
        'type' => GuestType::ALUMNI->value,
        'nric' => '876543-12-1280',
        'remarks' => 'This is first guest'
    ]);

    $this->assertDatabaseCount($this->table, 1);

    $payload = [
        'name' => [
            'en' => 'Guest 1',
            'zh' => '客人',
        ],
        'email' => '<EMAIL>',
        'phone_number' => '+60123456789',
        'type' => 'NONEXIST',
        'nric' => '876543-12-1280',
        'remarks' => 'This is first guest'
    ];

    $response = $this->postJson(route("$this->routeNamePrefix.create"), $payload)->json();

    $this->assertDatabaseCount($this->table, 1);
    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['status'])->toBe('ERROR')
        ->and($response['code'])->toBe(422)
        ->and($response['error'])->toBe([
            'email' => [
                'The email has already been taken.'
            ],
            'phone_number' => [
                'The phone number has already been taken.'
            ],
            'nric' => [
                'The nric has already been taken.',
                'The nric field must be a number.',
                'The nric field must be 12 digits.'
            ],
            'type' => [
                'The selected type is invalid.'
            ]
        ]);
});

test('destroy', function () {
    $guest = Guest::factory()->create();
    $other_guests = Guest::factory(3)->create();

    $this->assertDatabaseCount($this->table, 4);

    //id not exist
    $response = $this->deleteJson(route("$this->routeNamePrefix.destroy", ['guest' => 9999]))->json();
    expect($response)->toHaveModelResourceNotFoundResponse();

    $this->assertDatabaseCount($this->table, 4);

    //delete success
    $response = $this->deleteJson(route("$this->routeNamePrefix.destroy", ['guest' => $guest->id]))->json();
    expect($response)->toHaveSuccessGeneralResponse();

    $this->assertDatabaseCount($this->table, 3);
    $this->assertDatabaseMissing($this->table, ['id' => $guest->id]);

    foreach ($other_guests as $other_guest) {
        $this->assertDatabaseHas($this->table, ['id' => $other_guest->id]);
    }
});
