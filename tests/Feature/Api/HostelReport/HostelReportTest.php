<?php

use App\Enums\ClassType;
use App\Enums\ExportType;
use App\Enums\Gender;
use App\Enums\GuardianType;
use App\Enums\HostelBlockType;
use App\Enums\HostelInOutType;
use App\Enums\HostelMeritDemeritType;
use App\Enums\HostelRoomBedStatus;
use App\Enums\HostelRoomGender;
use App\Models\Card;
use App\Models\ClassModel;
use App\Models\Employee;
use App\Models\Grade;
use App\Models\GuardianStudent;
use App\Models\HostelBedAssignment;
use App\Models\HostelBedByYearView;
use App\Models\HostelBlock;
use App\Models\HostelInOutRecord;
use App\Models\HostelMeritDemeritSetting;
use App\Models\HostelRewardPunishmentRecord;
use App\Models\HostelRewardPunishmentSetting;
use App\Models\HostelRoom;
use App\Models\HostelRoomBed;
use App\Models\Media;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\SemesterYearSetting;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\User;
use App\Services\ReportPrintService;
use App\Services\Reports\HostelReportService;
use Barryvdh\Snappy\Facades\SnappyPdf;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PermissionSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Laravel\Sanctum\Sanctum;
use Mockery\MockInterface;

beforeEach(function () {
    $this->seed([
        InternationalizationSeeder::class,
        PermissionSeeder::class,
    ]);

    app()->setLocale('en');

    $this->withHeaders([
        'Accept-Language' => 'en'
    ]);

    $user = User::factory()->create([
        'password' => Hash::make('123456'),
    ]);

    $user->assignRole('Super Admin');

    Sanctum::actingAs($user);

    $this->routeNamePrefix = 'reports.hostels.';

    Storage::fake('s3-downloads');
});


test('reportByBordersNameList', function () {
    $semester_setting = SemesterSetting::factory()->create();

    $grade = Grade::factory()->create();

    $semester_class = SemesterClass::factory()->create();

    $payload = [
        'report_language' => 'en',
        'export_type' => ExportType::EXCEL->value,
        'semester_setting_id' => $semester_setting->id,
        'grade_id' => $grade->id,
        'semester_class_id' => $semester_class->id,
    ];

    // Report content already tested in HostelReportServiceTest
    $this->mock(HostelReportService::class, function (MockInterface $mock) use ($payload) {
        $mock->shouldReceive('setExportType')
            ->once()
            ->with($payload['export_type'])
            ->andReturnSelf();

        $mock->shouldReceive('setReportViewName')
            ->once()
            ->with('reports.hostels.by-boarders-name-list')
            ->andReturnSelf();

        $mock->shouldReceive('setFileName')
            ->once()
            ->with(Mockery::type('string'))
            ->andReturnSelf();

        $mock->shouldReceive('getHostelBoarderListReportData')
            ->once()
            ->with($payload)
            ->andReturn(['url' => 'http://localhost:8000/storage/s3-downloads/123456']);
    });

    $file_url = $this->getJson(route($this->routeNamePrefix . 'by-boarders-name-list', $payload))->json();

    expect($file_url)->toHaveSuccessGeneralResponse()
        ->toHaveKey('data.url', 'http://localhost:8000/storage/s3-downloads/123456');
});


test('getAvailableBedReportData, preview data', function () {
    $block = HostelBlock::factory()->create(['type' => HostelBlockType::STUDENT->value]);
    $room = HostelRoom::factory()->create(['name' => 'Room_A111', 'hostel_block_id' => $block->id, 'capacity' => 11]);
    $room_occupied_count = 3;
    $room_available_count = 8;
    HostelRoomBed::factory($room_occupied_count)->create([
        'hostel_room_id' => $room->id,
        'status' => HostelRoomBedStatus::OCCUPIED,  // 3 beds occupied
    ]);
    HostelRoomBed::factory($room_available_count)->create([
        'hostel_room_id' => $room->id,
        'status' => HostelRoomBedStatus::AVAILABLE,  // 8 beds available
    ]);

    $payload = [
        'report_language' => 'en',
    ];

    $response = $this->getJson(route($this->routeNamePrefix . 'by-available-bed', $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])
        ->toEqual([
            [
                'id' => $room->id,
                'name' => $room->name,
                'capacity' => ($room_occupied_count + $room_available_count),
                'occupied_beds' => $room_occupied_count,
                'available_beds' => $room_available_count,
            ]
        ]);
});

test('getAvailableBedReportData, download excel', function () {
    $block = HostelBlock::factory()->create(['type' => HostelBlockType::STUDENT->value]);
    $room = HostelRoom::factory()->create(['name' => 'Room_A111', 'hostel_block_id' => $block->id, 'capacity' => 11]);
    $room_occupied_count = 3;
    $room_available_count = 8;
    HostelRoomBed::factory($room_occupied_count)->create([
        'hostel_room_id' => $room->id,
        'status' => HostelRoomBedStatus::OCCUPIED,  // 3 beds occupied
    ]);
    HostelRoomBed::factory($room_available_count)->create([
        'hostel_room_id' => $room->id,
        'status' => HostelRoomBedStatus::AVAILABLE,  // 8 beds available
    ]);

    $payload = [
        'report_language' => 'en',
        'export_type' => ExportType::EXCEL->value,
    ];

    // Report content already tested in HostelReportServiceTest
    $this->mock(HostelReportService::class, function (MockInterface $mock) use ($payload) {
        $mock->shouldReceive('setExportType')
            ->once()
            ->with($payload['export_type'])
            ->andReturnSelf();

        $mock->shouldReceive('setReportViewName')
            ->once()
            ->with('reports.hostels.by-available-bed')
            ->andReturnSelf();

        $mock->shouldReceive('setFileName')
            ->once()
            ->with(Mockery::type('string'))
            ->andReturnSelf();

        $mock->shouldReceive('getAvailableBedReportData')
            ->once()
            ->with($payload)
            ->andReturn(['url' => 'http://localhost:8000/storage/s3-downloads/123456']);
    });

    $response = $this->getJson(route($this->routeNamePrefix . 'by-available-bed', $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])
        ->toEqual('http://localhost:8000/storage/s3-downloads/123456');
});


test('getCheckoutRecordReportData, preview data', function () {
    $user_student_1 = User::factory()->withStudent()->create();

    $student_1 = $user_student_1->student;

    $semester_setting = SemesterSetting::factory()->create([
        'from' => '2024-01-01',
        'to' => '2024-06-30',
    ]);
    $next_semester_setting = SemesterSetting::factory()->create([
        'from' => '2024-07-01',
        'to' => '2024-12-31',
    ]);

    $first_grade = Grade::factory()->create();
    $next_grade = Grade::factory()->create();

    $first_class = ClassModel::factory()->create(['grade_id' => $first_grade->id]);
    $next_class = ClassModel::factory()->create(['grade_id' => $next_grade->id]);

    $first_sem_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $first_class->id,
    ]);

    $next_sem_class = SemesterClass::factory()->create([
        'semester_setting_id' => $next_semester_setting->id,
        'class_id' => $next_class->id,
    ]);

    $sc1 = StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $first_sem_class->id,
        'student_id' => $student_1->id,
        'class_enter_date' => '2024-01-01',
    ]);

    $next_sc = StudentClass::factory()->create([
        'semester_setting_id' => $next_semester_setting->id,
        'semester_class_id' => $next_sem_class->id,
        'student_id' => $student_1->id,
        'class_enter_date' => '2024-07-01',
    ]);

    $boy_hostel_block = HostelBlock::factory()->create([
        'name->en' => 'Block B',
        'type' => HostelBlockType::STUDENT->value,
    ]);

    $boy_room_1 = HostelRoom::factory()->create([
        'name' => 'Room B1',
        'gender' => HostelRoomGender::MALE->value,
        'hostel_block_id' => $boy_hostel_block->id,
    ]);

    $beds = HostelRoomBed::factory(5)->state(new Sequence(
        [
            'name' => 'B1-101',
            'hostel_room_id' => $boy_room_1->id,
            'is_active' => true,
            'status' => HostelRoomBedStatus::OCCUPIED->value
        ],
        [
            'name' => 'B1-102',
            'hostel_room_id' => $boy_room_1->id,
            'is_active' => true,
            'status' => HostelRoomBedStatus::OCCUPIED->value
        ],
    ))->create();

    $bed_assignments = HostelBedAssignment::factory(2)->state(new Sequence(
        [
            'assignable_type' => Student::class,
            'assignable_id' => $student_1->id,
            'hostel_room_bed_id' => $beds[1]->id, // B1-102
            'start_date' => '2024-01-01',
            'end_date' => '2024-06-30',
        ],
        [
            'assignable_type' => Student::class,
            'assignable_id' => $student_1->id,
            'hostel_room_bed_id' => $beds[0]->id, // B1-101
            'start_date' => '2024-07-01',
            'end_date' => '2024-12-31',

        ],
    ))->create();

    $payload = [
        'report_language' => 'en',
        'year' => $semester_setting->semesterYearSetting->year,
        'semester_setting_id' => $semester_setting->id,
    ];

    $response = $this->getJson(route($this->routeNamePrefix . 'by-checkout-record', $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])
        ->toEqual([
            [
                'id' => $bed_assignments[1]->id,
                'start_date' => $bed_assignments[1]->start_date->toISOString(),
                'end_date' => $bed_assignments[1]->end_date->toISOString(),
                'hostel_room_bed_id' => $bed_assignments[1]->hostel_room_bed_id,
                'remarks' => $bed_assignments[1]->remarks,
                'class_name' => $first_class->getTranslations('name'), // class_name will get from chosen semester_setting_id
                'grade_name' => $first_grade->getTranslations('name'), // grade_name will get from chosen semester_setting_id
                'bed_name' => 'B1-101',
                'room_name' => 'Room B1',
                'student_name' => $student_1->getTranslations('name'),
                'phone_number' => $student_1->phone_number,
                'student_number' => $student_1->student_number,
                'nric' => $student_1->nric,
                'guardian' => null,
                'block_name' => $bed_assignments[1]->bed->hostelRoom->hostelBlock->getTranslation('name', app()->getLocale()),
            ],
            [
                'id' => $bed_assignments[0]->id,
                'start_date' => $bed_assignments[0]->start_date->toISOString(),
                'end_date' => $bed_assignments[0]->end_date->toISOString(),
                'hostel_room_bed_id' => $bed_assignments[0]->hostel_room_bed_id,
                'remarks' => $bed_assignments[0]->remarks,
                'class_name' => $first_class->getTranslations('name'), // class_name will get from chosen semester_setting_id
                'grade_name' => $first_grade->getTranslations('name'), // grade_name will get from chosen semester_setting_id
                'bed_name' => 'B1-102',
                'room_name' => 'Room B1',
                'student_name' => $student_1->getTranslations('name'),
                'phone_number' => $student_1->phone_number,
                'student_number' => $student_1->student_number,
                'nric' => $student_1->nric,
                'guardian' => null,
                'block_name' => $bed_assignments[0]->bed->hostelRoom->hostelBlock->getTranslation('name', app()->getLocale()),
            ],
        ]);
});

test('getCheckoutRecordReportData, download excel', function () {
    $user_student_1 = User::factory()->withStudent()->create();

    $student_1 = $user_student_1->student;

    $semester_setting = SemesterSetting::factory()->create([
        'from' => '2024-01-01',
        'to' => '2024-06-30',
    ]);
    $next_semester_setting = SemesterSetting::factory()->create([
        'from' => '2024-07-01',
        'to' => '2024-12-31',
    ]);

    $first_grade = Grade::factory()->create();
    $next_grade = Grade::factory()->create();

    $first_class = ClassModel::factory()->create(['grade_id' => $first_grade->id]);
    $next_class = ClassModel::factory()->create(['grade_id' => $next_grade->id]);

    $first_sem_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $first_class->id,
    ]);

    $next_sem_class = SemesterClass::factory()->create([
        'semester_setting_id' => $next_semester_setting->id,
        'class_id' => $next_class->id,
    ]);

    $sc1 = StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $first_sem_class->id,
        'student_id' => $student_1->id,
        'class_enter_date' => '2024-01-01',
    ]);

    $next_sc = StudentClass::factory()->create([
        'semester_setting_id' => $next_semester_setting->id,
        'semester_class_id' => $next_sem_class->id,
        'student_id' => $student_1->id,
        'class_enter_date' => '2024-07-01',
    ]);

    $bed_assignment_1 = HostelBedAssignment::factory()->create([
        'assignable_type' => Student::class,
        'assignable_id' => $student_1->id,
        'start_date' => '2024-01-01',
        'end_date' => '2024-06-30',
    ]);

    $bed_assignment_2 = HostelBedAssignment::factory()->create([
        'assignable_type' => Student::class,
        'assignable_id' => $student_1->id,
        'start_date' => '2024-07-01',
        'end_date' => '2024-12-31',
    ]);

    $payload = [
        'report_language' => 'en',
        'export_type' => ExportType::EXCEL->value,
        'year' => $semester_setting->semesterYearSetting->year,
        'semester_setting_id' => $semester_setting->id,
    ];

    // Report content already tested in HostelReportServiceTest
    $this->mock(HostelReportService::class, function (MockInterface $mock) use ($payload) {
        $mock->shouldReceive('setExportType')
            ->once()
            ->with($payload['export_type'])
            ->andReturnSelf();

        $mock->shouldReceive('setReportViewName')
            ->once()
            ->with('reports.hostels.by-checkout-record')
            ->andReturnSelf();

        $mock->shouldReceive('setFileName')
            ->once()
            ->with(Mockery::type('string'))
            ->andReturnSelf();

        $mock->shouldReceive('getCheckoutRecordReportData')
            ->once()
            ->with($payload)
            ->andReturn(['url' => 'http://localhost:8000/storage/s3-downloads/123456']);
    });

    $response = $this->getJson(route($this->routeNamePrefix . 'by-checkout-record', $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])
        ->toEqual('http://localhost:8000/storage/s3-downloads/123456');
});


test('getBoardersListInfoReportData, preview data', function () {
    $sem1 = SemesterSetting::factory()->create([
        'name' => 'Semester 1'
    ]);

    $sem2 = SemesterSetting::factory()->create([
        'name' => 'Semester 2'
    ]);

    $grade1 = Grade::factory()->create([
        'name->en' => 'Junior 1'
    ]);

    $grade2 = Grade::factory()->create([
        'name->en' => 'Junior 2'
    ]);

    $classes = ClassModel::factory(2)->create(new Sequence(
        [
            'name->en' => 'J111',
            'type' => ClassType::PRIMARY,
            'grade_id' => $grade1->id
        ],
        [
            'name->en' => 'J211',
            'type' => ClassType::PRIMARY,
            'grade_id' => $grade2->id
        ],
    ));

    $semester_classes = SemesterClass::factory(4)->create(new Sequence(
        [
            'semester_setting_id' => $sem1->id,
            'class_id' => $classes[0]->id // Grade 1
        ],
        [
            'semester_setting_id' => $sem1->id,
            'class_id' => $classes[1]->id // Grade 2
        ],
        [
            'semester_setting_id' => $sem2->id,
            'class_id' => $classes[0]->id // Grade 1
        ],
        [
            'semester_setting_id' => $sem2->id,
            'class_id' => $classes[1]->id // Grade 2
        ],
    ));

    $students = Student::factory()->count(4)->create(new Sequence(
        [
            'is_hostel' => true,
            'name->en' => 'Student 1',
        ],
        [
            'is_hostel' => true,
            'name->en' => 'Student 2',
        ],
        [
            'is_hostel' => true,
            'name->en' => 'Student 3',
        ],
        [
            'is_hostel' => true,
            'name->en' => 'Student 4',
        ],
    ));

    $user_guardians = User::factory(2)->withGuardian()->create(); // 2 guardians for $students[0]

    $gs1 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians[0]->guardian->id,
        'type' => GuardianType::FATHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[0]->id
    ]);

    $gs2 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians[1]->guardian->id,
        'type' => GuardianType::MOTHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[0]->id
    ]);

    $user_guardians2 = User::factory(2)->withGuardian()->create(); // 2 guardians for $students[1]

    $gs3 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians2[0]->guardian->id,
        'type' => GuardianType::FATHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[1]->id
    ]);

    $gs4 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians2[1]->guardian->id,
        'type' => GuardianType::MOTHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[1]->id
    ]);

    $user_guardians3 = User::factory(2)->withGuardian()->create(); // 2 guardians for $students[2]

    $gs5 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians3[0]->guardian->id,
        'type' => GuardianType::FATHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[2]->id
    ]);

    $gs6 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians3[1]->guardian->id,
        'type' => GuardianType::MOTHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[2]->id
    ]);


    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[0]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[1]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[2]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[3]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    $hostel_block = HostelBlock::factory()->create([
        'name->en' => 'Block A',
    ]);

    $hostel_room1 = HostelRoom::factory()->create([
        'name' => 'Room 1',
        'hostel_block_id' => $hostel_block->id,
    ]);

    $hostel_bed2 = HostelRoomBed::factory()->create([
        'name' => 'Bed 2',
        'hostel_room_id' => $hostel_room1->id,
    ]);

    $hostel_bed1 = HostelRoomBed::factory()->create([
        'name' => 'Bed 1',
        'hostel_room_id' => $hostel_room1->id,
    ]);


    $bed_assignment1 = HostelBedAssignment::factory()->create([
        'hostel_room_bed_id' => $hostel_bed1->id,
        'assignable_id' => $students[0]->id,
        'assignable_type' => Student::class,
        'start_date' => now()->toDateString()
    ]);

    $bed_assignment2 = HostelBedAssignment::factory()->create([
        'hostel_room_bed_id' => $hostel_bed2->id,
        'assignable_id' => $students[1]->id,
        'assignable_type' => Student::class,
        'start_date' => now()->toDateString()
    ]);

    $payload = [
        'report_language' => 'en',
        'semester_setting_id' => $sem1->id,
    ];

    $response = $this->getJson(route($this->routeNamePrefix . 'by-boarders-list-info', $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse();

    expect($response['data'])->toMatchArray([
        [
            'student_id' => $students[2]->id,
            'student_number' => $students[2]->student_number,
            'student_name' => $students[2]->getTranslations('name'),
            'student_nric' => $students[2]->nric,
            'student_address' => $students[2]->address,
            'student_phone_number' => $students[2]->phone_number,
            'student_date_of_birth' => $students[2]->date_of_birth,
            'start_date' => null,
            'end_date' => null,
            'block_name' => null,
            'bed_name' => null,
            'room_name' => null,
            'class_name' => $classes[0]->getTranslations('name'),
            'grade_name' => $grade1->getTranslations('name'),
            'guardians' => [
                [
                    'id' => $user_guardians3[0]->guardian->id,
                    'name' => $user_guardians3[0]->guardian->getTranslations('name'),
                    'nric' => $user_guardians3[0]->guardian->nric,
                    'phone_number' => $user_guardians3[0]->guardian->phone_number,
                    'pivot' => $gs5->toArray(),
                ],
            ]
        ],
        [
            'student_id' => $students[3]->id,
            'student_number' => $students[3]->student_number,
            'student_name' => $students[3]->getTranslations('name'),
            'student_nric' => $students[3]->nric,
            'student_address' => $students[3]->address,
            'student_phone_number' => $students[3]->phone_number,
            'student_date_of_birth' => $students[3]->date_of_birth,
            'start_date' => null,
            'end_date' => null,
            'block_name' => null,
            'bed_name' => null,
            'room_name' => null,
            'class_name' => $classes[0]->getTranslations('name'),
            'grade_name' => $grade1->getTranslations('name'),
            'guardians' => []
        ],
        [
            'student_id' => $students[0]->id,
            'student_number' => $students[0]->student_number,
            'student_name' => $students[0]->getTranslations('name'),
            'student_nric' => $students[0]->nric,
            'student_address' => $students[0]->address,
            'student_phone_number' => $students[0]->phone_number,
            'student_date_of_birth' => $students[0]->date_of_birth,
            'start_date' => $bed_assignment1->start_date->toDateString(),
            'end_date' => $bed_assignment1->end_date?->toDateString(),
            'block_name' => $hostel_block->getTranslation('name', app()->getLocale()),
            'bed_name' => $hostel_bed1->name,
            'room_name' => $hostel_room1->name,
            'class_name' => $classes[0]->getTranslations('name'),
            'grade_name' => $grade1->getTranslations('name'),
            'guardians' => [
                [
                    'id' => $user_guardians[0]->guardian->id,
                    'name' => $user_guardians[0]->guardian->getTranslations('name'),
                    'nric' => $user_guardians[0]->guardian->nric,
                    'phone_number' => $user_guardians[0]->guardian->phone_number,
                    'pivot' => $gs1->toArray(),
                ],
            ]
        ],
        [
            'student_id' => $students[1]->id,
            'student_number' => $students[1]->student_number,
            'student_name' => $students[1]->getTranslations('name'),
            'student_nric' => $students[1]->nric,
            'student_address' => $students[1]->address,
            'student_phone_number' => $students[1]->phone_number,
            'student_date_of_birth' => $students[1]->date_of_birth,
            'start_date' => $bed_assignment2->start_date->toDateString(),
            'end_date' => $bed_assignment2->end_date?->toDateString(),
            'block_name' => $hostel_block->getTranslation('name', app()->getLocale()),
            'bed_name' => $hostel_bed2->name,
            'room_name' => $hostel_room1->name,
            'class_name' => $classes[0]->getTranslations('name'),
            'grade_name' => $grade1->getTranslations('name'),
            'guardians' => [
                [
                    'id' => $user_guardians2[0]->guardian->id,
                    'name' => $user_guardians2[0]->guardian->getTranslations('name'),
                    'nric' => $user_guardians2[0]->guardian->nric,
                    'phone_number' => $user_guardians2[0]->guardian->phone_number,
                    'pivot' => $gs3->toArray(),
                ],
            ]
        ],
    ]);
});

test('getBoardersListInfoReportData, download excel', function () {
    $sem1 = SemesterSetting::factory()->create([
        'name' => 'Semester 1'
    ]);

    $sem2 = SemesterSetting::factory()->create([
        'name' => 'Semester 2'
    ]);

    $grade1 = Grade::factory()->create([
        'name->en' => 'Junior 1'
    ]);

    $grade2 = Grade::factory()->create([
        'name->en' => 'Junior 2'
    ]);

    $classes = ClassModel::factory(2)->create(new Sequence(
        [
            'name->en' => 'J111',
            'type' => ClassType::PRIMARY,
            'grade_id' => $grade1->id
        ],
        [
            'name->en' => 'J211',
            'type' => ClassType::PRIMARY,
            'grade_id' => $grade2->id
        ],
    ));

    $semester_classes = SemesterClass::factory(4)->create(new Sequence(
        [
            'semester_setting_id' => $sem1->id,
            'class_id' => $classes[0]->id // Grade 1
        ],
        [
            'semester_setting_id' => $sem1->id,
            'class_id' => $classes[1]->id // Grade 2
        ],
        [
            'semester_setting_id' => $sem2->id,
            'class_id' => $classes[0]->id // Grade 1
        ],
        [
            'semester_setting_id' => $sem2->id,
            'class_id' => $classes[1]->id // Grade 2
        ],
    ));

    $students = Student::factory()->count(4)->create([
        'is_hostel' => true
    ]);

    $user_guardians = User::factory(2)->withGuardian()->create(); // 2 guardians for $students[0]

    $gs1 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians[0]->guardian->id,
        'type' => GuardianType::FATHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[0]->id
    ]);

    $gs2 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians[1]->guardian->id,
        'type' => GuardianType::MOTHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[0]->id
    ]);

    $user_guardians2 = User::factory(2)->withGuardian()->create(); // 2 guardians for $students[1]

    $gs3 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians2[0]->guardian->id,
        'type' => GuardianType::FATHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[1]->id
    ]);

    $gs4 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians2[1]->guardian->id,
        'type' => GuardianType::MOTHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[1]->id
    ]);

    $user_guardians3 = User::factory(2)->withGuardian()->create(); // 2 guardians for $students[2]

    $gs5 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians3[0]->guardian->id,
        'type' => GuardianType::FATHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[2]->id
    ]);

    $gs6 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians3[1]->guardian->id,
        'type' => GuardianType::MOTHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[2]->id
    ]);


    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[0]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[1]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[2]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[3]->id,
        'class_enter_date' => now()->toDateString()
    ]);


    $hostel_room1 = HostelRoom::factory()->create([
        'name' => 'Room 1'
    ]);

    $hostel_bed2 = HostelRoomBed::factory()->create([
        'name' => 'Bed 2',
        'hostel_room_id' => $hostel_room1->id,
    ]);

    $hostel_bed1 = HostelRoomBed::factory()->create([
        'name' => 'Bed 1',
        'hostel_room_id' => $hostel_room1->id,
    ]);


    $bed_assignment1 = HostelBedAssignment::factory()->create([
        'hostel_room_bed_id' => $hostel_bed1->id,
        'assignable_id' => $students[0]->id,
        'assignable_type' => Student::class,
        'start_date' => now()->toDateString()
    ]);

    $bed_assignment2 = HostelBedAssignment::factory()->create([
        'hostel_room_bed_id' => $hostel_bed2->id,
        'assignable_id' => $students[1]->id,
        'assignable_type' => Student::class,
        'start_date' => now()->toDateString()
    ]);

    $payload = [
        'report_language' => 'en',
        'export_type' => ExportType::EXCEL->value,
        'semester_setting_id' => $sem1->id,
    ];

    // Report content already tested in HostelReportServiceTest
    $this->mock(HostelReportService::class, function (MockInterface $mock) use ($payload) {
        $mock->shouldReceive('setExportType')
            ->once()
            ->with($payload['export_type'])
            ->andReturnSelf();

        $mock->shouldReceive('setReportViewName')
            ->once()
            ->with('reports.hostels.by-boarders-list-information')
            ->andReturnSelf();

        $mock->shouldReceive('setFileName')
            ->once()
            ->with(Mockery::type('string'))
            ->andReturnSelf();

        $mock->shouldReceive('getBoardersListInfoReportData')
            ->once()
            ->with($payload)
            ->andReturn(['url' => 'http://localhost:8000/storage/s3-downloads/123456']);
    });

    $response = $this->getJson(route($this->routeNamePrefix . 'by-boarders-list-info', $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])
        ->toEqual('http://localhost:8000/storage/s3-downloads/123456');
});


test('getBoardersContactInfoReportData, preview data', function () {
    $sem1 = SemesterSetting::factory()->create([
        'name' => 'Semester 1'
    ]);

    $sem2 = SemesterSetting::factory()->create([
        'name' => 'Semester 2'
    ]);

    $grade1 = Grade::factory()->create([
        'name->en' => 'Junior 1'
    ]);

    $grade2 = Grade::factory()->create([
        'name->en' => 'Junior 2'
    ]);

    $classes = ClassModel::factory(2)->create(new Sequence(
        [
            'name->en' => 'J111',
            'type' => ClassType::PRIMARY,
            'grade_id' => $grade1->id
        ],
        [
            'name->en' => 'J211',
            'type' => ClassType::PRIMARY,
            'grade_id' => $grade2->id
        ],
    ));

    $semester_classes = SemesterClass::factory(4)->create(new Sequence(
        [
            'semester_setting_id' => $sem1->id,
            'class_id' => $classes[0]->id // Grade 1
        ],
        [
            'semester_setting_id' => $sem1->id,
            'class_id' => $classes[1]->id // Grade 2
        ],
        [
            'semester_setting_id' => $sem2->id,
            'class_id' => $classes[0]->id // Grade 1
        ],
        [
            'semester_setting_id' => $sem2->id,
            'class_id' => $classes[1]->id // Grade 2
        ],
    ));

    $students = Student::factory()->count(4)->create(new Sequence(
        [
            'is_hostel' => true,
            'name->en' => 'Student 1',
        ],
        [
            'is_hostel' => true,
            'name->en' => 'Student 2',
        ],
        [
            'is_hostel' => true,
            'name->en' => 'Student 3',
        ],
        [
            'is_hostel' => true,
            'name->en' => 'Student 4',
        ],
    ));

    $user_guardians = User::factory(2)->withGuardian()->create(); // 2 guardians for $students[0]

    $gs1 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians[0]->guardian->id,
        'type' => GuardianType::FATHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[0]->id
    ]);

    $gs2 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians[1]->guardian->id,
        'type' => GuardianType::MOTHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[0]->id
    ]);

    $user_guardians2 = User::factory(2)->withGuardian()->create(); // 2 guardians for $students[1]

    $gs3 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians2[0]->guardian->id,
        'type' => GuardianType::FATHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[1]->id
    ]);

    $gs4 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians2[1]->guardian->id,
        'type' => GuardianType::MOTHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[1]->id
    ]);

    $user_guardians3 = User::factory(2)->withGuardian()->create(); // 2 guardians for $students[2]

    $gs5 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians3[0]->guardian->id,
        'type' => GuardianType::FATHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[2]->id
    ]);

    $gs6 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians3[1]->guardian->id,
        'type' => GuardianType::MOTHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[2]->id
    ]);


    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[0]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[1]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[2]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[3]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    $hostel_block = HostelBlock::factory()->create([
        'name->en' => 'Block A',
    ]);

    $hostel_room1 = HostelRoom::factory()->create([
        'name' => 'Room 1',
        'hostel_block_id' => $hostel_block->id,
    ]);

    $hostel_bed2 = HostelRoomBed::factory()->create([
        'name' => 'Bed 2',
        'hostel_room_id' => $hostel_room1->id,
    ]);

    $hostel_bed1 = HostelRoomBed::factory()->create([
        'name' => 'Bed 1',
        'hostel_room_id' => $hostel_room1->id,
    ]);


    $bed_assignment1 = HostelBedAssignment::factory()->create([
        'hostel_room_bed_id' => $hostel_bed1->id,
        'assignable_id' => $students[0]->id,
        'assignable_type' => Student::class,
        'start_date' => now()->toDateString()
    ]);

    $bed_assignment2 = HostelBedAssignment::factory()->create([
        'hostel_room_bed_id' => $hostel_bed2->id,
        'assignable_id' => $students[1]->id,
        'assignable_type' => Student::class,
        'start_date' => now()->toDateString()
    ]);

    $payload = [
        'report_language' => 'en',
        'semester_setting_id' => $sem1->id,
    ];

    $response = $this->getJson(route($this->routeNamePrefix . 'by-boarders-contact-info', $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse();

    expect($response['data'])->toMatchArray([
        [
            'student_id' => $students[2]->id,
            'student_number' => $students[2]->student_number,
            'student_name' => $students[2]->getTranslations('name'),
            'student_nric' => $students[2]->nric,
            'student_address' => $students[2]->address,
            'student_phone_number' => $students[2]->phone_number,
            'student_date_of_birth' => $students[2]->date_of_birth,
            'start_date' => null,
            'end_date' => null,
            'block_name' => null,
            'bed_name' => null,
            'room_name' => null,
            'class_name' => $classes[0]->getTranslations('name'),
            'grade_name' => $grade1->getTranslations('name'),
            'guardians' => [
                [
                    'id' => $user_guardians3[0]->guardian->id,
                    'name' => $user_guardians3[0]->guardian->getTranslations('name'),
                    'nric' => $user_guardians3[0]->guardian->nric,
                    'phone_number' => $user_guardians3[0]->guardian->phone_number,
                    'pivot' => $gs5->toArray(),
                ],
                [
                    'id' => $user_guardians3[1]->guardian->id,
                    'name' => $user_guardians3[1]->guardian->getTranslations('name'),
                    'nric' => $user_guardians3[1]->guardian->nric,
                    'phone_number' => $user_guardians3[1]->guardian->phone_number,
                    'pivot' => $gs6->toArray(),
                ],
            ]
        ],
        [
            'student_id' => $students[3]->id,
            'student_number' => $students[3]->student_number,
            'student_name' => $students[3]->getTranslations('name'),
            'student_nric' => $students[3]->nric,
            'student_address' => $students[3]->address,
            'student_phone_number' => $students[3]->phone_number,
            'student_date_of_birth' => $students[3]->date_of_birth,
            'start_date' => null,
            'end_date' => null,
            'block_name' => null,
            'bed_name' => null,
            'room_name' => null,
            'class_name' => $classes[0]->getTranslations('name'),
            'grade_name' => $grade1->getTranslations('name'),
            'guardians' => []
        ],
        [
            'student_id' => $students[0]->id,
            'student_number' => $students[0]->student_number,
            'student_name' => $students[0]->getTranslations('name'),
            'student_nric' => $students[0]->nric,
            'student_address' => $students[0]->address,
            'student_phone_number' => $students[0]->phone_number,
            'student_date_of_birth' => $students[0]->date_of_birth,
            'start_date' => $bed_assignment1->start_date->toDateString(),
            'end_date' => $bed_assignment1->end_date?->toDateString(),
            'block_name' => $hostel_block->getTranslation('name', app()->getLocale()),
            'bed_name' => $hostel_bed1->name,
            'room_name' => $hostel_room1->name,
            'class_name' => $classes[0]->getTranslations('name'),
            'grade_name' => $grade1->getTranslations('name'),
            'guardians' => [
                [
                    'id' => $user_guardians[0]->guardian->id,
                    'name' => $user_guardians[0]->guardian->getTranslations('name'),
                    'nric' => $user_guardians[0]->guardian->nric,
                    'phone_number' => $user_guardians[0]->guardian->phone_number,
                    'pivot' => $gs1->toArray(),
                ],
                [
                    'id' => $user_guardians[1]->guardian->id,
                    'name' => $user_guardians[1]->guardian->getTranslations('name'),
                    'nric' => $user_guardians[1]->guardian->nric,
                    'phone_number' => $user_guardians[1]->guardian->phone_number,
                    'pivot' => $gs2->toArray(),
                ],
            ]
        ],
        [
            'student_id' => $students[1]->id,
            'student_number' => $students[1]->student_number,
            'student_name' => $students[1]->getTranslations('name'),
            'student_nric' => $students[1]->nric,
            'student_address' => $students[1]->address,
            'student_phone_number' => $students[1]->phone_number,
            'student_date_of_birth' => $students[1]->date_of_birth,
            'start_date' => $bed_assignment2->start_date->toDateString(),
            'end_date' => $bed_assignment2->end_date?->toDateString(),
            'block_name' => $hostel_block->getTranslation('name', app()->getLocale()),
            'bed_name' => $hostel_bed2->name,
            'room_name' => $hostel_room1->name,
            'class_name' => $classes[0]->getTranslations('name'),
            'grade_name' => $grade1->getTranslations('name'),
            'guardians' => [
                [
                    'id' => $user_guardians2[0]->guardian->id,
                    'name' => $user_guardians2[0]->guardian->getTranslations('name'),
                    'nric' => $user_guardians2[0]->guardian->nric,
                    'phone_number' => $user_guardians2[0]->guardian->phone_number,
                    'pivot' => $gs3->toArray(),
                ],
                [
                    'id' => $user_guardians2[1]->guardian->id,
                    'name' => $user_guardians2[1]->guardian->getTranslations('name'),
                    'nric' => $user_guardians2[1]->guardian->nric,
                    'phone_number' => $user_guardians2[1]->guardian->phone_number,
                    'pivot' => $gs4->toArray(),
                ],
            ]
        ],
    ]);
});

test('getBoardersContactInfoReportData, download excel', function () {
    $sem1 = SemesterSetting::factory()->create([
        'name' => 'Semester 1'
    ]);

    $sem2 = SemesterSetting::factory()->create([
        'name' => 'Semester 2'
    ]);

    $grade1 = Grade::factory()->create([
        'name->en' => 'Junior 1'
    ]);

    $grade2 = Grade::factory()->create([
        'name->en' => 'Junior 2'
    ]);

    $classes = ClassModel::factory(2)->create(new Sequence(
        [
            'name->en' => 'J111',
            'type' => ClassType::PRIMARY,
            'grade_id' => $grade1->id
        ],
        [
            'name->en' => 'J211',
            'type' => ClassType::PRIMARY,
            'grade_id' => $grade2->id
        ],
    ));

    $semester_classes = SemesterClass::factory(4)->create(new Sequence(
        [
            'semester_setting_id' => $sem1->id,
            'class_id' => $classes[0]->id // Grade 1
        ],
        [
            'semester_setting_id' => $sem1->id,
            'class_id' => $classes[1]->id // Grade 2
        ],
        [
            'semester_setting_id' => $sem2->id,
            'class_id' => $classes[0]->id // Grade 1
        ],
        [
            'semester_setting_id' => $sem2->id,
            'class_id' => $classes[1]->id // Grade 2
        ],
    ));

    $students = Student::factory()->count(4)->create([
        'is_hostel' => true
    ]);

    $user_guardians = User::factory(2)->withGuardian()->create(); // 2 guardians for $students[0]

    $gs1 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians[0]->guardian->id,
        'type' => GuardianType::FATHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[0]->id
    ]);

    $gs2 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians[1]->guardian->id,
        'type' => GuardianType::MOTHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[0]->id
    ]);

    $user_guardians2 = User::factory(2)->withGuardian()->create(); // 2 guardians for $students[1]

    $gs3 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians2[0]->guardian->id,
        'type' => GuardianType::FATHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[1]->id
    ]);

    $gs4 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians2[1]->guardian->id,
        'type' => GuardianType::MOTHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[1]->id
    ]);

    $user_guardians3 = User::factory(2)->withGuardian()->create(); // 2 guardians for $students[2]

    $gs5 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians3[0]->guardian->id,
        'type' => GuardianType::FATHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[2]->id
    ]);

    $gs6 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians3[1]->guardian->id,
        'type' => GuardianType::MOTHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[2]->id
    ]);


    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[0]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[1]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[2]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[3]->id,
        'class_enter_date' => now()->toDateString()
    ]);


    $hostel_room1 = HostelRoom::factory()->create([
        'name' => 'Room 1'
    ]);

    $hostel_bed2 = HostelRoomBed::factory()->create([
        'name' => 'Bed 2',
        'hostel_room_id' => $hostel_room1->id,
    ]);

    $hostel_bed1 = HostelRoomBed::factory()->create([
        'name' => 'Bed 1',
        'hostel_room_id' => $hostel_room1->id,
    ]);


    $bed_assignment1 = HostelBedAssignment::factory()->create([
        'hostel_room_bed_id' => $hostel_bed1->id,
        'assignable_id' => $students[0]->id,
        'assignable_type' => Student::class,
        'start_date' => now()->toDateString()
    ]);

    $bed_assignment2 = HostelBedAssignment::factory()->create([
        'hostel_room_bed_id' => $hostel_bed2->id,
        'assignable_id' => $students[1]->id,
        'assignable_type' => Student::class,
        'start_date' => now()->toDateString()
    ]);

    $payload = [
        'report_language' => 'en',
        'export_type' => ExportType::EXCEL->value,
        'semester_setting_id' => $sem1->id,
    ];

    // Report content already tested in HostelReportServiceTest
    $this->mock(HostelReportService::class, function (MockInterface $mock) use ($payload) {
        $mock->shouldReceive('setExportType')
            ->once()
            ->with($payload['export_type'])
            ->andReturnSelf();

        $mock->shouldReceive('setReportViewName')
            ->once()
            ->with('reports.hostels.by-boarders-contact-information')
            ->andReturnSelf();

        $mock->shouldReceive('setFileName')
            ->once()
            ->with(Mockery::type('string'))
            ->andReturnSelf();

        $mock->shouldReceive('getBoardersContactInfoReportData')
            ->once()
            ->with($payload)
            ->andReturn(['url' => 'http://localhost:8000/storage/s3-downloads/123456']);
    });

    $response = $this->getJson(route($this->routeNamePrefix . 'by-boarders-contact-info', $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])
        ->toEqual('http://localhost:8000/storage/s3-downloads/123456');
});


test('getBoardersDateOfBirthReportData, preview data', function () {
    $sem1 = SemesterSetting::factory()->create([
        'name' => 'Semester 1'
    ]);

    $sem2 = SemesterSetting::factory()->create([
        'name' => 'Semester 2'
    ]);

    $grade1 = Grade::factory()->create([
        'name->en' => 'Junior 1'
    ]);

    $grade2 = Grade::factory()->create([
        'name->en' => 'Junior 2'
    ]);

    $classes = ClassModel::factory(2)->create(new Sequence(
        [
            'name->en' => 'J111',
            'type' => ClassType::PRIMARY,
            'grade_id' => $grade1->id
        ],
        [
            'name->en' => 'J211',
            'type' => ClassType::PRIMARY,
            'grade_id' => $grade2->id
        ],
    ));

    $semester_classes = SemesterClass::factory(4)->create(new Sequence(
        [
            'semester_setting_id' => $sem1->id,
            'class_id' => $classes[0]->id // Grade 1
        ],
        [
            'semester_setting_id' => $sem1->id,
            'class_id' => $classes[1]->id // Grade 2
        ],
        [
            'semester_setting_id' => $sem2->id,
            'class_id' => $classes[0]->id // Grade 1
        ],
        [
            'semester_setting_id' => $sem2->id,
            'class_id' => $classes[1]->id // Grade 2
        ],
    ));

    $students = Student::factory(4)->create(new Sequence(
        [
            'name->en' => 'John Jones',
            'is_hostel' => true,

        ],
        [
            'name->en' => 'Dwayne',
            'is_hostel' => true,

        ],
        [
            'name->en' => 'Karen',
            'is_hostel' => true,
        ],
        [
            'name->en' => 'Kaley',
            'is_hostel' => true,
        ],
    ));

    $user_guardians = User::factory(2)->withGuardian()->create(); // 2 guardians for $students[0]

    $gs1 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians[0]->guardian->id,
        'type' => GuardianType::FATHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[0]->id
    ]);

    $gs2 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians[1]->guardian->id,
        'type' => GuardianType::MOTHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[0]->id
    ]);

    $user_guardians2 = User::factory(2)->withGuardian()->create(); // 2 guardians for $students[1]

    $gs3 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians2[0]->guardian->id,
        'type' => GuardianType::FATHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[1]->id
    ]);

    $gs4 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians2[1]->guardian->id,
        'type' => GuardianType::MOTHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[1]->id
    ]);

    $user_guardians3 = User::factory(2)->withGuardian()->create(); // 2 guardians for $students[2]

    $gs5 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians3[0]->guardian->id,
        'type' => GuardianType::FATHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[2]->id
    ]);

    $gs6 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians3[1]->guardian->id,
        'type' => GuardianType::MOTHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[2]->id
    ]);


    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[0]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[1]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[2]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[3]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    $hostel_block = HostelBlock::factory()->create([
        'name->en' => 'Block A',
    ]);

    $hostel_room1 = HostelRoom::factory()->create([
        'name' => 'Room 1',
        'hostel_block_id' => $hostel_block->id,
    ]);

    $hostel_bed2 = HostelRoomBed::factory()->create([
        'name' => 'Bed 2',
        'hostel_room_id' => $hostel_room1->id,
    ]);

    $hostel_bed1 = HostelRoomBed::factory()->create([
        'name' => 'Bed 1',
        'hostel_room_id' => $hostel_room1->id,
    ]);


    $bed_assignment1 = HostelBedAssignment::factory()->create([
        'hostel_room_bed_id' => $hostel_bed1->id,
        'assignable_id' => $students[0]->id,
        'assignable_type' => Student::class,
        'start_date' => now()->toDateString()
    ]);

    $bed_assignment2 = HostelBedAssignment::factory()->create([
        'hostel_room_bed_id' => $hostel_bed2->id,
        'assignable_id' => $students[1]->id,
        'assignable_type' => Student::class,
        'start_date' => now()->toDateString()
    ]);


    $payload = [
        'report_language' => 'en',
        'semester_setting_id' => $sem1->id,
    ];

    $response = $this->getJson(route($this->routeNamePrefix . 'by-boarders-date-of-birth', $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse();

    expect($response['data'])->toMatchArray([
        [
            'student_id' => $students[3]->id,
            'student_number' => $students[3]->student_number,
            'student_name' => $students[3]->getTranslations('name'),
            'student_nric' => $students[3]->nric,
            'student_address' => $students[3]->address,
            'student_phone_number' => $students[3]->phone_number,
            'student_date_of_birth' => $students[3]->date_of_birth,
            'start_date' => null,
            'end_date' => null,
            'block_name' => null,
            'bed_name' => null,
            'room_name' => null,
            'class_name' => $classes[0]->getTranslations('name'),
            'grade_name' => $grade1->getTranslations('name'),
            'guardians' => []
        ],
        [
            'student_id' => $students[2]->id,
            'student_number' => $students[2]->student_number,
            'student_name' => $students[2]->getTranslations('name'),
            'student_nric' => $students[2]->nric,
            'student_address' => $students[2]->address,
            'student_phone_number' => $students[2]->phone_number,
            'student_date_of_birth' => $students[2]->date_of_birth,
            'start_date' => null,
            'end_date' => null,
            'block_name' => null,
            'bed_name' => null,
            'room_name' => null,
            'class_name' => $classes[0]->getTranslations('name'),
            'grade_name' => $grade1->getTranslations('name'),
            'guardians' => []
        ],
        [
            'student_id' => $students[0]->id,
            'student_number' => $students[0]->student_number,
            'student_name' => $students[0]->getTranslations('name'),
            'student_nric' => $students[0]->nric,
            'student_address' => $students[0]->address,
            'student_phone_number' => $students[0]->phone_number,
            'student_date_of_birth' => $students[0]->date_of_birth,
            'start_date' => $bed_assignment1->start_date->toDateString(),
            'end_date' => $bed_assignment1->end_date?->toDateString(),
            'block_name' => $hostel_block->getTranslation('name', app()->getLocale()),
            'bed_name' => $hostel_bed1->name,
            'room_name' => $hostel_room1->name,
            'class_name' => $classes[0]->getTranslations('name'),
            'grade_name' => $grade1->getTranslations('name'),
            'guardians' => []
        ],
        [
            'student_id' => $students[1]->id,
            'student_number' => $students[1]->student_number,
            'student_name' => $students[1]->getTranslations('name'),
            'student_nric' => $students[1]->nric,
            'student_address' => $students[1]->address,
            'student_phone_number' => $students[1]->phone_number,
            'student_date_of_birth' => $students[1]->date_of_birth,
            'start_date' => $bed_assignment2->start_date->toDateString(),
            'end_date' => $bed_assignment2->end_date?->toDateString(),
            'block_name' => $hostel_block->getTranslation('name', app()->getLocale()),
            'bed_name' => $hostel_bed2->name,
            'room_name' => $hostel_room1->name,
            'class_name' => $classes[0]->getTranslations('name'),
            'grade_name' => $grade1->getTranslations('name'),
            'guardians' => []
        ],
    ]);
});

test('getBoardersDateOfBirthReportData, download excel', function () {
    $sem1 = SemesterSetting::factory()->create([
        'name' => 'Semester 1'
    ]);

    $sem2 = SemesterSetting::factory()->create([
        'name' => 'Semester 2'
    ]);

    $grade1 = Grade::factory()->create([
        'name->en' => 'Junior 1'
    ]);

    $grade2 = Grade::factory()->create([
        'name->en' => 'Junior 2'
    ]);

    $classes = ClassModel::factory(2)->create(new Sequence(
        [
            'name->en' => 'J111',
            'type' => ClassType::PRIMARY,
            'grade_id' => $grade1->id
        ],
        [
            'name->en' => 'J211',
            'type' => ClassType::PRIMARY,
            'grade_id' => $grade2->id
        ],
    ));

    $semester_classes = SemesterClass::factory(4)->create(new Sequence(
        [
            'semester_setting_id' => $sem1->id,
            'class_id' => $classes[0]->id // Grade 1
        ],
        [
            'semester_setting_id' => $sem1->id,
            'class_id' => $classes[1]->id // Grade 2
        ],
        [
            'semester_setting_id' => $sem2->id,
            'class_id' => $classes[0]->id // Grade 1
        ],
        [
            'semester_setting_id' => $sem2->id,
            'class_id' => $classes[1]->id // Grade 2
        ],
    ));

    $students = Student::factory()->count(4)->create([
        'is_hostel' => true
    ]);

    $user_guardians = User::factory(2)->withGuardian()->create(); // 2 guardians for $students[0]

    $gs1 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians[0]->guardian->id,
        'type' => GuardianType::FATHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[0]->id
    ]);

    $gs2 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians[1]->guardian->id,
        'type' => GuardianType::MOTHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[0]->id
    ]);

    $user_guardians2 = User::factory(2)->withGuardian()->create(); // 2 guardians for $students[1]

    $gs3 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians2[0]->guardian->id,
        'type' => GuardianType::FATHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[1]->id
    ]);

    $gs4 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians2[1]->guardian->id,
        'type' => GuardianType::MOTHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[1]->id
    ]);

    $user_guardians3 = User::factory(2)->withGuardian()->create(); // 2 guardians for $students[2]

    $gs5 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians3[0]->guardian->id,
        'type' => GuardianType::FATHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[2]->id
    ]);

    $gs6 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians3[1]->guardian->id,
        'type' => GuardianType::MOTHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[2]->id
    ]);


    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[0]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[1]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[2]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[3]->id,
        'class_enter_date' => now()->toDateString()
    ]);


    $hostel_room1 = HostelRoom::factory()->create([
        'name' => 'Room 1'
    ]);

    $hostel_bed2 = HostelRoomBed::factory()->create([
        'name' => 'Bed 2',
        'hostel_room_id' => $hostel_room1->id,
    ]);

    $hostel_bed1 = HostelRoomBed::factory()->create([
        'name' => 'Bed 1',
        'hostel_room_id' => $hostel_room1->id,
    ]);


    $bed_assignment1 = HostelBedAssignment::factory()->create([
        'hostel_room_bed_id' => $hostel_bed1->id,
        'assignable_id' => $students[0]->id,
        'assignable_type' => Student::class,
        'start_date' => now()->toDateString()
    ]);

    $bed_assignment2 = HostelBedAssignment::factory()->create([
        'hostel_room_bed_id' => $hostel_bed2->id,
        'assignable_id' => $students[1]->id,
        'assignable_type' => Student::class,
        'start_date' => now()->toDateString()
    ]);

    $payload = [
        'report_language' => 'en',
        'export_type' => ExportType::EXCEL->value,
        'semester_setting_id' => $sem1->id,
    ];

    // Report content already tested in HostelReportServiceTest
    $this->mock(HostelReportService::class, function (MockInterface $mock) use ($payload) {
        $mock->shouldReceive('setExportType')
            ->once()
            ->with($payload['export_type'])
            ->andReturnSelf();

        $mock->shouldReceive('setReportViewName')
            ->once()
            ->with('reports.hostels.by-boarders-date-of-birth')
            ->andReturnSelf();

        $mock->shouldReceive('setFileName')
            ->once()
            ->with(Mockery::type('string'))
            ->andReturnSelf();

        $mock->shouldReceive('getBoardersDateOfBirthReportData')
            ->once()
            ->with($payload)
            ->andReturn(['url' => 'http://localhost:8000/storage/s3-downloads/123456']);
    });

    $response = $this->getJson(route($this->routeNamePrefix . 'by-boarders-date-of-birth', $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])
        ->toEqual('http://localhost:8000/storage/s3-downloads/123456');
});


test('getBoardersStaybackReportData, preview data', function () {
    $sem1 = SemesterSetting::factory()->create([
        'name' => '2025 Semester 1'
    ]);

    $grades = Grade::factory(2)->create(new Sequence(
        [
            'name->en' => 'Junior 1'
        ],
    ));

    $classes = ClassModel::factory(2)->create(new Sequence(
        [
            'name->en' => 'J111',
            'type' => ClassType::PRIMARY,
            'grade_id' => $grades[0]->id
        ],
    ));

    $semester_classes = SemesterClass::factory(4)->create(new Sequence(
        [
            'semester_setting_id' => $sem1->id,
            'class_id' => $classes[0]->id // Grade 1
        ],
    ));


    $students = Student::factory(4)->create(new Sequence(
        [
            'name->en' => 'John Jones',
            'gender' => Gender::MALE->value,
            'is_hostel' => true,

        ],
        [
            'name->en' => 'Dwayne',
            'gender' => Gender::MALE->value,
            'is_hostel' => true,

        ],
        [
            'name->en' => 'Karen',
            'gender' => Gender::FEMALE->value,
            'is_hostel' => true,

        ],
    ));

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[0]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[1]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[2]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    $hostel_rooms = HostelRoom::factory(2)->create(new Sequence(
        [
            'name' => 'Room 1'
        ],
        [
            'name' => 'Room 2'
        ],
    ));


    $hostel_beds = HostelRoomBed::factory(4)->create(new Sequence(
        [
            'name' => 'Bed 1',
            'hostel_room_id' => $hostel_rooms[1]->id,
        ],
        [
            'name' => 'Bed 2',
            'hostel_room_id' => $hostel_rooms[1]->id,
        ],
        [
            'name' => 'Bed 3',
            'hostel_room_id' => $hostel_rooms[0]->id,
        ],
    ));


    $hostel_bed_assignments = HostelBedAssignment::factory(4)->create(new Sequence(
        [
            'hostel_room_bed_id' => $hostel_beds[0]->id,
            'assignable_id' => $students[0]->id,
            'assignable_type' => Student::class,
            'start_date' => now()->toDateString(),
        ],
        [
            'hostel_room_bed_id' => $hostel_beds[1]->id,
            'assignable_id' => $students[1]->id,
            'assignable_type' => Student::class,
            'start_date' => now()->toDateString(),
        ],
        [
            'hostel_room_bed_id' => $hostel_beds[2]->id,
            'assignable_id' => $students[2]->id,
            'assignable_type' => Student::class,
            'start_date' => now()->toDateString(),
        ],
    ));

    $payload = [
        'report_language' => 'en',
        'gender' => Gender::MALE->value,
    ];

    $response = $this->getJson(route($this->routeNamePrefix . 'by-boarders-stayback', $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse();

    expect($response['data'])
        ->toHaveCount(2)
        ->toHaveKey('0.student_name.en', 'John Jones')
        ->toHaveKey('1.student_name.en', 'Dwayne');
});

test('getBoardersStaybackReportData, download excel', function () {
    $sem1 = SemesterSetting::factory()->create([
        'name' => '2025 Semester 1'
    ]);

    $grades = Grade::factory(2)->create(new Sequence(
        [
            'name->en' => 'Junior 1'
        ],
    ));

    $classes = ClassModel::factory(2)->create(new Sequence(
        [
            'name->en' => 'J111',
            'type' => ClassType::PRIMARY,
            'grade_id' => $grades[0]->id
        ],
    ));

    $semester_classes = SemesterClass::factory(4)->create(new Sequence(
        [
            'semester_setting_id' => $sem1->id,
            'class_id' => $classes[0]->id // Grade 1
        ],
    ));


    $students = Student::factory(4)->create(new Sequence(
        [
            'name->en' => 'John Jones',
            'gender' => Gender::MALE->value,
            'is_hostel' => true,

        ],
        [
            'name->en' => 'Dwayne',
            'gender' => Gender::MALE->value,
            'is_hostel' => true,

        ],
        [
            'name->en' => 'Karen',
            'gender' => Gender::FEMALE->value,
            'is_hostel' => true,

        ],
    ));

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[0]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[1]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[2]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    $hostel_rooms = HostelRoom::factory(2)->create(new Sequence(
        [
            'name' => 'Room 1'
        ],
        [
            'name' => 'Room 2'
        ],
    ));


    $hostel_beds = HostelRoomBed::factory(4)->create(new Sequence(
        [
            'name' => 'Bed 1',
            'hostel_room_id' => $hostel_rooms[1]->id,
        ],
        [
            'name' => 'Bed 2',
            'hostel_room_id' => $hostel_rooms[1]->id,
        ],
        [
            'name' => 'Bed 3',
            'hostel_room_id' => $hostel_rooms[0]->id,
        ],
    ));


    $hostel_bed_assignments = HostelBedAssignment::factory(4)->create(new Sequence(
        [
            'hostel_room_bed_id' => $hostel_beds[0]->id,
            'assignable_id' => $students[0]->id,
            'assignable_type' => Student::class,
            'start_date' => now()->toDateString(),
        ],
        [
            'hostel_room_bed_id' => $hostel_beds[1]->id,
            'assignable_id' => $students[1]->id,
            'assignable_type' => Student::class,
            'start_date' => now()->toDateString(),
        ],
        [
            'hostel_room_bed_id' => $hostel_beds[2]->id,
            'assignable_id' => $students[2]->id,
            'assignable_type' => Student::class,
            'start_date' => now()->toDateString(),
        ],
    ));


    $payload = [
        'report_language' => 'en',
        'export_type' => ExportType::EXCEL->value,
        'gender' => Gender::MALE->value,
    ];


    // Report content already tested in HostelReportServiceTest
    $this->mock(HostelReportService::class, function (MockInterface $mock) use ($payload) {
        $mock->shouldReceive('setExportType')
            ->once()
            ->with($payload['export_type'])
            ->andReturnSelf();

        $mock->shouldReceive('setReportViewName')
            ->once()
            ->with('reports.hostels.by-boarders-stayback')
            ->andReturnSelf();

        $mock->shouldReceive('setFileName')
            ->once()
            ->with(Mockery::type('string'))
            ->andReturnSelf();

        $mock->shouldReceive('getBoardersStaybackReportData')
            ->once()
            ->with($payload)
            ->andReturn(['url' => 'http://localhost:8000/storage/s3-downloads/123456']);
    });

    $response = $this->getJson(route($this->routeNamePrefix . 'by-boarders-stayback', $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])
        ->toEqual('http://localhost:8000/storage/s3-downloads/123456');
});

test('getEmployeeLodgingReportData, download excel', function () {
    $employee_blocks = HostelBlock::factory(2)->employeeBlock()->create(new Sequence(
        [
            'name->en' => 'Block A',
        ],
        [
            'name->en' => 'Block B',
        ],
    ));

    $hostel_rooms = HostelRoom::factory(3)->create(new Sequence(
        [
            'name' => 'Room 100',
            'hostel_block_id' => $employee_blocks[0]->id, // Block A
        ],
        [
            'name' => 'Room 200',
            'hostel_block_id' => $employee_blocks[0]->id, // Block A
        ],
        [
            'name' => 'Room ZZZ',
            'hostel_block_id' => $employee_blocks[1]->id, // Block B
        ],
    ));

    $hostel_beds = HostelRoomBed::factory(3)->create(new Sequence(
        [
            'name' => 'Bed 1',
            'hostel_room_id' => $hostel_rooms[0]->id,
            'status' => HostelRoomBedStatus::OCCUPIED->value,
        ],
        [
            'name' => 'Bed 1',
            'hostel_room_id' => $hostel_rooms[1]->id,
            'status' => HostelRoomBedStatus::OCCUPIED->value,
        ],
        [
            'name' => 'Bed 1',
            'hostel_room_id' => $hostel_rooms[2]->id,
            'status' => HostelRoomBedStatus::OCCUPIED->value,
        ],
    ));

    $employees = Employee::factory(4)->create(new Sequence(
        [
            'name->en' => 'Albert',
            'gender' => Gender::MALE->value,
            'is_hostel' => true,
            'employment_start_date' => '2020-01-20',
        ],
        [
            'name->en' => 'CEO King G',
            'gender' => Gender::MALE->value,
            'is_hostel' => true,
            'employment_start_date' => '2024-05-01',
        ],
        [
            'name->en' => 'Karen',
            'gender' => Gender::FEMALE->value,
            'is_hostel' => true,
            'employment_start_date' => '2022-02-05',
        ],
        [
            'name->en' => 'Koko',
            'gender' => Gender::FEMALE->value,
            'is_hostel' => true, // no room assignment, expect to be excluded
            'employment_start_date' => '2023-01-10',
        ],
    ));

    $hostel_bed_assignments = HostelBedAssignment::factory(4)->create(new Sequence(
        [
            'hostel_room_bed_id' => $hostel_beds[0]->id,
            'assignable_id' => $employees[0]->id,
            'assignable_type' => Employee::class,
            'start_date' => now()->toDateString(),
        ],
        [
            'hostel_room_bed_id' => $hostel_beds[1]->id,
            'assignable_id' => $employees[1]->id,
            'assignable_type' => Employee::class,
            'start_date' => now()->toDateString(),
        ],
        [
            'hostel_room_bed_id' => $hostel_beds[2]->id,
            'assignable_id' => $employees[2]->id,
            'assignable_type' => Employee::class,
            'start_date' => now()->toDateString(),
        ],
    ));

    $payload = [
        'report_language' => 'en',
        'export_type' => ExportType::EXCEL->value,
    ];


    // Report content already tested in HostelReportServiceTest
    $this->mock(HostelReportService::class, function (MockInterface $mock) use ($payload) {
        $mock->shouldReceive('setExportType')
            ->once()
            ->with($payload['export_type'])
            ->andReturnSelf();

        $mock->shouldReceive('setReportViewName')
            ->once()
            ->with('reports.hostels.by-employee-lodging')
            ->andReturnSelf();

        $mock->shouldReceive('setFileName')
            ->once()
            ->with(Mockery::type('string'))
            ->andReturnSelf();

        $mock->shouldReceive('getEmployeeLodgingReportData')
            ->once()
            ->with($payload)
            ->andReturn(['url' => 'http://localhost:8000/storage/s3-downloads/123456']);
    });

    $response = $this->getJson(route($this->routeNamePrefix . 'by-employee-lodging', $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])
        ->toEqual('http://localhost:8000/storage/s3-downloads/123456');
});


test('getGoHomeOrOutReportData, preview data', function () {
    $warden = Employee::factory()->create([
        'name->en' => 'Warden Johnson',
    ]);

    $yet_return_students = Student::factory(2)->create(new Sequence(
        [
            'name->en' => 'Yet Return 1 Jon',
        ],
        [
            'name->en' => 'Yet Return 2 Kon',
        ],
    ));

    $leave_school_students = Student::factory(2)->create(new Sequence(
        [
            'name->en' => 'Leave School 1 David',
        ],
        [
            'name->en' => 'Leave School 2 Paulo',
        ],
    ));

    $returned_students = Student::factory(2)->create(new Sequence(
        [
            'name->en' => 'Returned 1 Charlie',
        ],
        [
            'name->en' => 'Returned 2 Frank',
        ],
    ));


    $cards = Card::factory(6)->create(new Sequence(
        [
            'card_number' => '111111',
            'userable_type' => Student::class,
            'userable_id' => $yet_return_students[0]->id,
        ],
        [
            'card_number' => '222222',
            'userable_type' => Student::class,
            'userable_id' => $yet_return_students[1]->id,
        ],
        [
            'card_number' => '333333',
            'userable_type' => Student::class,
            'userable_id' => $leave_school_students[0]->id,
        ],
        [
            'card_number' => '444444',
            'userable_type' => Student::class,
            'userable_id' => $leave_school_students[1]->id,
        ],
        [
            'card_number' => '5555555',
            'userable_type' => Student::class,
            'userable_id' => $returned_students[0]->id,
        ],
        [
            'card_number' => '666666',
            'userable_type' => Student::class,
            'userable_id' => $returned_students[1]->id,
        ],
    ));

    $hostel_block = HostelBlock::factory()->create([
        'name->en' => 'Primary Block',
    ]);

    $hostel_rooms = HostelRoom::factory(2)->create(new Sequence(
        [
            'name' => 'Room 1',
            'hostel_block_id' => $hostel_block->id,  // 3 bed in this room
        ],
        [
            'name' => 'Room AAA',
            'hostel_block_id' => $hostel_block->id,  // 3 bed in this room
        ],
    ));

    $hostel_beds = HostelRoomBed::factory(6)->create(new Sequence(
        [
            'name' => 'Bed 1',
            'hostel_room_id' => $hostel_rooms[0]->id,
        ],
        [
            'name' => 'Bed 2',
            'hostel_room_id' => $hostel_rooms[0]->id,
        ],
        [
            'name' => 'Bed 3',
            'hostel_room_id' => $hostel_rooms[0]->id,
        ],
        [
            'name' => 'Bed A1',
            'hostel_room_id' => $hostel_rooms[1]->id,
        ],
        [
            'name' => 'Bed A2',
            'hostel_room_id' => $hostel_rooms[1]->id,
        ],
        [
            'name' => 'Bed A2',
            'hostel_room_id' => $hostel_rooms[1]->id,
        ],
    ));

    $hostel_bed_assignments = HostelBedAssignment::factory(6)->create(new Sequence(
        [
            'hostel_room_bed_id' => $hostel_beds[0]->id,
            'assignable_id' => $yet_return_students[0]->id,
            'assignable_type' => Student::class,
            'start_date' => now()->toDateString(),
        ],
        [
            'hostel_room_bed_id' => $hostel_beds[1]->id,
            'assignable_id' => $yet_return_students[1]->id,
            'assignable_type' => Student::class,
            'start_date' => now()->toDateString(),
        ],
        [
            'hostel_room_bed_id' => $hostel_beds[2]->id,
            'assignable_id' => $leave_school_students[0]->id,
            'assignable_type' => Student::class,
            'start_date' => now()->toDateString(),
        ],
        [
            'hostel_room_bed_id' => $hostel_beds[3]->id,
            'assignable_id' => $leave_school_students[1]->id,
            'assignable_type' => Student::class,
            'start_date' => now()->toDateString(),
        ],
        [
            'hostel_room_bed_id' => $hostel_beds[4]->id,
            'assignable_id' => $returned_students[0]->id,
            'assignable_type' => Student::class,
            'start_date' => now()->toDateString(),
        ],
        [
            'hostel_room_bed_id' => $hostel_beds[5]->id,
            'assignable_id' => $returned_students[1]->id,
            'assignable_type' => Student::class,
            'start_date' => now()->toDateString(),
        ],
    ));

    $yet_returned_records = HostelInOutRecord::factory(2)->create(new Sequence(
        [
            'student_id' => $yet_return_students[0]->id,
            'check_out_datetime' => '2024-01-20 01:00:00',
            'check_in_datetime' => null,
            'type' => HostelInOutType::HOME->value,
            'check_out_by' => $warden->user_id,
        ],
        [
            'student_id' => $yet_return_students[1]->id,
            'check_out_datetime' => '2024-01-21 02:00:00',
            'check_in_datetime' => null,
            'type' => HostelInOutType::OUTING->value,
            'check_out_by' => $warden->user_id,
        ],
    ));

    $leave_school_records = HostelInOutRecord::factory(2)->create(new Sequence(
        [
            'student_id' => $leave_school_students[0]->id,
            'check_out_datetime' => '2024-02-20 08:00:00',
            'check_in_datetime' => null,
            'type' => HostelInOutType::HOME->value,
            'check_out_by' => $warden->user_id,
        ],
        [
            'student_id' => $leave_school_students[1]->id,
            'check_out_datetime' => '2024-02-20 08:00:00',
            'check_in_datetime' => '2024-02-21 08:00:00',
            'type' => HostelInOutType::OUTING->value,
            'check_out_by' => $warden->user_id,
            'check_in_by' => $warden->user_id,
        ],
    ));

    $returned_records = HostelInOutRecord::factory(2)->create(new Sequence(
        [
            'student_id' => $returned_students[0]->id,
            'check_out_datetime' => '2024-03-01 08:00:00',
            'check_in_datetime' => '2024-03-02 08:00:00',
            'type' => HostelInOutType::HOME->value,
            'check_out_by' => $warden->user_id,
            'check_in_by' => $warden->user_id,
        ],
        [
            'student_id' => $returned_students[1]->id,
            'check_out_datetime' => '2024-03-01 08:00:00',
            'check_in_datetime' => '2024-03-02 08:00:00',
            'type' => HostelInOutType::OUTING->value,
            'check_out_by' => $warden->user_id,
            'check_in_by' => $warden->user_id,
        ],
    ));


    $payload = [
        'report_language' => 'en',
        'yet_return' => true,
        'in_out_type' => HostelInOutType::HOME->value,
        'date_from' => '2024-01-19',
        'date_to' => '2024-01-21',
    ];

    $response = $this->getJson(route($this->routeNamePrefix . 'by-boarders-go-home-or-out', $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse();

    expect($response['data'])
        ->toHaveCount(1)
        ->toHaveKey('0.student_name.en', $yet_return_students[0]->getTranslation('name', 'en'));
});

test('getGoHomeOrOutReportData, download excel', function () {
    HostelInOutRecord::factory(2)->create(new Sequence(
        [
            'check_out_datetime' => '2024-03-01 08:00:00',
            'check_in_datetime' => '2024-03-02 08:00:00',
            'type' => HostelInOutType::HOME->value,
        ],
        [
            'check_out_datetime' => '2024-03-01 08:00:00',
            'check_in_datetime' => '2024-03-02 08:00:00',
            'type' => HostelInOutType::OUTING->value,
        ],
    ));

    $payload = [
        'report_language' => 'en',
        'export_type' => ExportType::EXCEL->value,
        'yet_return' => true,
        'in_out_type' => HostelInOutType::HOME->value,
        'date_from' => '2024-01-19',
        'date_to' => '2024-01-21',
    ];

    // Report content already tested in HostelReportServiceTest
    $this->mock(HostelReportService::class, function (MockInterface $mock) use ($payload) {
        $mock->shouldReceive('setExportType')
            ->once()
            ->with($payload['export_type'])
            ->andReturnSelf();

        $mock->shouldReceive('setReportViewName')
            ->once()
            ->with('reports.hostels.by-boarders-go-home')
            ->andReturnSelf();

        $mock->shouldReceive('setFileName')
            ->once()
            ->with(Mockery::type('string'))
            ->andReturnSelf();

        $mock->shouldReceive('getGoHomeOrOutReportData')
            ->once()
            ->with($payload)
            ->andReturn(['url' => 'http://localhost:8000/storage/s3-downloads/123456']);
    });

    $response = $this->getJson(route($this->routeNamePrefix . 'by-boarders-go-home-or-out', $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])
        ->toEqual('http://localhost:8000/storage/s3-downloads/123456');
});


test('getChangeRoomReportData, preview data', function () {
    $sem_year = SemesterYearSetting::create([
        'year' => 2025
    ]);

    $sem1 = SemesterSetting::factory()->create([
        'name' => '2025 Semester 1',
        'semester_year_setting_id' => $sem_year->id,
    ]);

    $grades = Grade::factory(2)->create(new Sequence(
        [
            'name->en' => 'Junior 1'
        ],
        [
            'name->en' => 'Junior 2'
        ],
    ));

    $classes = ClassModel::factory(2)->create(new Sequence(
        [
            'name->en' => 'J111',
            'type' => ClassType::PRIMARY,
            'grade_id' => $grades[0]->id
        ],
        [
            'name->en' => 'J211',
            'type' => ClassType::PRIMARY,
            'grade_id' => $grades[1]->id
        ],
    ));

    $semester_classes = SemesterClass::factory(4)->create(new Sequence(
        [
            'semester_setting_id' => $sem1->id,
            'class_id' => $classes[0]->id // Grade 1
        ],
        [
            'semester_setting_id' => $sem1->id,
            'class_id' => $classes[1]->id // Grade 2
        ],
    ));


    $students = Student::factory(4)->create(new Sequence(
        [
            'name->en' => 'John Jones',
            'gender' => Gender::MALE->value,
            'is_hostel' => true,

        ],
        [
            'name->en' => 'Dwayne',
            'gender' => Gender::MALE->value,
            'is_hostel' => true,

        ],
        [
            'name->en' => 'Frank',
            'gender' => Gender::MALE->value,
            'is_hostel' => true,

        ],
        [
            'name->en' => 'Charlie',
            'gender' => Gender::MALE->value,
            'is_hostel' => true,

        ],
    ));

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[0]->id,
        'class_enter_date' => '2025-01-05',
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[1]->id,
        'class_enter_date' => '2025-01-05',
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[1]->id, // Grade 2
        'student_id' => $students[2]->id,
        'class_enter_date' => '2025-01-05',
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[1]->id, // Grade 2
        'student_id' => $students[3]->id,
        'class_enter_date' => '2025-01-05',
    ]);


    $hostel_rooms = HostelRoom::factory(2)->create(new Sequence(
        [
            'name' => 'Room 1'
        ],
        [
            'name' => 'Room 2'
        ],
    ));


    $hostel_beds = HostelRoomBed::factory(4)->create(new Sequence(
        [
            'name' => 'Bed 1',
            'hostel_room_id' => $hostel_rooms[0]->id,    // Room 1
        ],
        [
            'name' => 'Bed 2',
            'hostel_room_id' => $hostel_rooms[0]->id,    // Room 1
        ],
        [
            'name' => 'Bed 3',
            'hostel_room_id' => $hostel_rooms[1]->id,    // Room 2
        ],
        [
            'name' => 'Bed 4',
            'hostel_room_id' => $hostel_rooms[1]->id,    // Room 2
        ],
    ));

    $hostel_bed_assignments = HostelBedAssignment::factory(4)->create(new Sequence(
        [
            'previous_hostel_room_bed_id' => $hostel_beds[3]->id,
            'hostel_room_bed_id' => $hostel_beds[0]->id,
            'assignable_id' => $students[0]->id,
            'assignable_type' => Student::class,
            'start_date' => '2025-01-01',
            'end_date' => '2025-12-20',
        ],
        [
            'previous_hostel_room_bed_id' => $hostel_beds[2]->id,
            'hostel_room_bed_id' => $hostel_beds[1]->id,
            'assignable_id' => $students[1]->id,
            'assignable_type' => Student::class,
            'start_date' => '2025-01-01',
            'end_date' => '2025-12-20',
        ],
        [
            'previous_hostel_room_bed_id' => $hostel_beds[1]->id,
            'hostel_room_bed_id' => $hostel_beds[2]->id,
            'assignable_id' => $students[2]->id,
            'assignable_type' => Student::class,
            'start_date' => '2025-01-01',
            'end_date' => '2025-12-20',
        ],
        [
            'previous_hostel_room_bed_id' => $hostel_beds[0]->id,
            'hostel_room_bed_id' => $hostel_beds[3]->id,
            'assignable_id' => $students[3]->id,
            'assignable_type' => Student::class,
            'start_date' => '2025-01-01',
            'end_date' => null, // havent checkout yet
        ],
    ));


    $payload = [
        'report_language' => 'en',
        'year' => $sem1->semesterYearSetting->year,
        'semester_setting_id' => $sem1->id,
    ];

    $response = $this->getJson(route($this->routeNamePrefix . 'by-change-room-record', $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])
        ->toHaveKey('0.student_name.en', 'John Jones')
        ->toHaveKey('1.student_name.en', 'Dwayne')
        ->toHaveKey('2.student_name.en', 'Frank')
        ->toHaveKey('3.student_name.en', 'Charlie');
});

test('getChangeRoomReportData, download excel', function () {
    $sem_year = SemesterYearSetting::create([
        'year' => 2025
    ]);

    $sem1 = SemesterSetting::factory()->create([
        'name' => '2025 Semester 1',
        'semester_year_setting_id' => $sem_year->id,
    ]);

    $hostel_bed_assignments = HostelBedAssignment::factory(4)->create(new Sequence(
        [
            'start_date' => '2025-01-01',
            'end_date' => '2025-12-20',
        ],
        [
            'start_date' => '2025-01-01',
            'end_date' => '2025-12-20',
        ],
    ));

    $payload = [
        'report_language' => 'en',
        'year' => $sem1->semesterYearSetting->year,
        'semester_setting_id' => $sem1->id,
        'export_type' => ExportType::EXCEL->value,
    ];

    // Report content already tested in HostelReportServiceTest
    $this->mock(HostelReportService::class, function (MockInterface $mock) use ($payload) {
        $mock->shouldReceive('setExportType')
            ->once()
            ->with($payload['export_type'])
            ->andReturnSelf();

        $mock->shouldReceive('setReportViewName')
            ->once()
            ->with('reports.hostels.by-change-room-record')
            ->andReturnSelf();

        $mock->shouldReceive('setFileName')
            ->once()
            ->with(Mockery::type('string'))
            ->andReturnSelf();

        $mock->shouldReceive('getChangeRoomReportData')
            ->once()
            ->with($payload)
            ->andReturn(['url' => 'http://localhost:8000/storage/s3-downloads/123456']);
    });

    $response = $this->getJson(route($this->routeNamePrefix . 'by-change-room-record', $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])
        ->toEqual('http://localhost:8000/storage/s3-downloads/123456');
});

test('reportByRewardPunishmentBlock return pdf', function () {

    $hostel_block = HostelBlock::factory()->create([
        'name->en' => fake()->name,
        'name->zh' => fake('zh_CN')->name,
        'code' => 'BLOCK-A',
        'type' => HostelBlockType::STUDENT
    ]);

    SnappyPdf::fake();
    HostelBedByYearView::refreshViewTable(false);

    $filters = [
        'report_language' => 'en',
        'export_type' => ExportType::PDF->value,
        'block_id' => $hostel_block->id,
        'date_from' => '2024-01-03',
        'date_to' => '2024-12-30',
    ];

    $filename = 'hostels-report-by-reward-punishment-block';
    $extension = '.pdf';

    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) use ($filename, $extension) {
        $mock->shouldReceive('upload')->twice()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->twice()->andReturn($filename . $extension);
    });

    $response = $this->getJson(
        route($this->routeNamePrefix . 'by-reward-punishment-block', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])->toMatch("/$filename/")
        ->toEndWith($extension);

    // date from and date to don't have same year
    $filters = [
        'report_language' => 'en',
        'export_type' => ExportType::PDF->value,
        'block_id' => $hostel_block->id,
        'date_from' => '2024-01-03',
        'date_to' => '2025-12-30',
    ];

    $response = $this->getJson(
        route($this->routeNamePrefix . 'by-reward-punishment-block', $filters)
    )->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'code' => '422',
            'error' => [
                'date_to' => [
                    'Date from and Date to must have the same year'
                ],
            ]
        ]);

    // block does not exist
    $filters = [
        'report_language' => 'en',
        'export_type' => ExportType::PDF->value,
        'block_id' => 10000,
        'date_from' => '2024-01-01',
        'date_to' => '2024-12-31',
    ];

    $response = $this->getJson(
        route($this->routeNamePrefix . 'by-reward-punishment-block', $filters)
    )->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'code' => '422',
            'error' => [
                'block_id' => [
                    'The selected block id is invalid.'
                ],
            ]
        ]);

    // filtering same date dont throw error
    $filters = [
        'report_language' => 'en',
        'export_type' => ExportType::PDF->value,
        'block_id' => $hostel_block->id,
        'date_from' => '2024-01-03',
        'date_to' => '2024-01-03',
    ];

    $response = $this->getJson(
        route($this->routeNamePrefix . 'by-reward-punishment-block', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])->toEndWith($extension);
});

test('reportByRewardPunishmentStudent return pdf', function () {

    // person in charge
    $person_in_charge_A = Employee::factory()->create();
    $person_in_charge_B = Employee::factory()->create();

    // student
    $student_A = Student::factory()->create();
    $student_B = Student::factory()->create();

    Media::factory()->create([
        'model_type' => Student::class,
        'model_id' => $student_A->id,
        'file_name' => 'first_file.png',
        'collection_name' => 'photo'
    ]);

    // hostel block sample data
    $hostel_block_A = HostelBlock::factory()->create([
        'name->en' => fake()->name,
        'name->zh' => fake('zh_CN')->name,
        'code' => 'BLOCK-A',
        'type' => HostelBlockType::STUDENT
    ]);

    $hostel_block_B = HostelBlock::factory()->create([
        'name->en' => fake()->name,
        'name->zh' => fake('zh_CN')->name,
        'code' => 'BLOCK-B',
        'type' => HostelBlockType::STUDENT
    ]);

    // hostel room sample data
    $hostel_room_A = HostelRoom::factory()->create([
        'hostel_block_id' => $hostel_block_A->id,
        'name' => strtoupper(fake()->bothify('Room ?##')),
        'gender' => HostelRoomGender::MALE->value,
        'capacity' => 100,
        'remarks' => fake()->realText(),
        'is_active' => true,
    ]);

    $hostel_room_B = HostelRoom::factory()->create([
        'hostel_block_id' => $hostel_block_B->id,
        'name' => strtoupper(fake()->bothify('Room ?##')),
        'gender' => HostelRoomGender::MALE->value,
        'capacity' => 100,
        'remarks' => fake()->realText(),
        'is_active' => true,
    ]);

    // hostel room bed sample data
    $hostel_room_bed_A = HostelRoomBed::factory()->create([
        'hostel_room_id' => $hostel_room_A->id,
        'name' => fake()->ean8(),
        'is_active' => true,
        'status' => HostelRoomBedStatus::OCCUPIED->value
    ]);

    $hostel_room_bed_B = HostelRoomBed::factory()->create([
        'hostel_room_id' => $hostel_room_B->id,
        'name' => fake()->ean8(),
        'is_active' => true,
        'status' => HostelRoomBedStatus::OCCUPIED->value
    ]);

    // hostel bedassignemnt sample data
    $hostel_bed_assignment_A = HostelBedAssignment::factory()->create([
        'assignable_id' => $student_A->id,
        'assignable_type' => Student::class,
        'hostel_room_bed_id' => $hostel_room_bed_A->id,
        'previous_hostel_room_bed_id' => null,
        'assigned_by' => User::factory(),
        'start_date' => strtotime("2024-04-30"),
        'end_date' => null,
        'remarks' => fake()->text(100),
    ]);

    $hostel_bed_assignment_B = HostelBedAssignment::factory()->create([
        'assignable_id' => $student_B->id,
        'assignable_type' => Student::class,
        'hostel_room_bed_id' => $hostel_room_bed_B->id,
        'previous_hostel_room_bed_id' => null,
        'assigned_by' => User::factory(),
        'start_date' => strtotime("2024-04-30"),
        'end_date' => null,
        'remarks' => fake()->text(100),
    ]);

    $hostel_bed_assignment_C = HostelBedAssignment::factory()->create([
        'assignable_id' => $student_B->id,
        'assignable_type' => Student::class,
        'hostel_room_bed_id' => $hostel_room_bed_B->id,
        'previous_hostel_room_bed_id' => null,
        'assigned_by' => User::factory(),
        'start_date' => strtotime("2023-04-30"),
        'end_date' => strtotime("2023-10-30"),
        'remarks' => fake()->text(100),
    ]);

    // merit demerit setting data
    $hostel_merit_demerit_setting_A = HostelMeritDemeritSetting::factory()->create([
        'type' => HostelMeritDemeritType::DEMERIT->value,
        'name' => 'Illegal Item -5 marks'
    ]);

    $hostel_merit_demerit_setting_B = HostelMeritDemeritSetting::factory()->create([
        'type' => HostelMeritDemeritType::DEMERIT->value,
        'name' => 'Other -5 marks'
    ]);

    $hostel_merit_demerit_setting_C = HostelMeritDemeritSetting::factory()->create([
        'type' => HostelMeritDemeritType::MERIT->value,
        'name' => 'Charity +2 marks'
    ]);

    // reward punishment setting data
    $hostel_reward_punishment_settings_A = HostelRewardPunishmentSetting::factory()->create([
        'hostel_merit_demerit_setting_id' => $hostel_merit_demerit_setting_A->id,
        'code' => fake()->uuid(),
        'name' => 'Bring Electronic item to school',
        'points' => -5
    ]);

    $hostel_reward_punishment_settings_B = HostelRewardPunishmentSetting::factory()->create([
        'hostel_merit_demerit_setting_id' => $hostel_merit_demerit_setting_B->id,
        'code' => fake()->uuid(),
        'name' => 'Did not clean toilet/room',
        'points' => -5
    ]);

    $hostel_reward_punishment_settings_C = HostelRewardPunishmentSetting::factory()->create([
        'hostel_merit_demerit_setting_id' => $hostel_merit_demerit_setting_C->id,
        'code' => fake()->uuid(),
        'name' => 'Volunteer at old folks home',
        'points' => 2

    ]);

    // hostel reward punishment record data
    $hostel_reward_punishment_records = HostelRewardPunishmentRecord::factory(3)->create(new Sequence(
        [
            'person_in_charge_id' => $person_in_charge_A->id,
            'student_id' => $student_A->id,
            'hostel_reward_punishment_setting_id' => $hostel_reward_punishment_settings_A->id,
            'remark' => 'Bad student',
            'date' => '2024-06-15'
        ],
        [
            'person_in_charge_id' => $person_in_charge_A->id,
            'student_id' => $student_A->id,
            'hostel_reward_punishment_setting_id' => $hostel_reward_punishment_settings_B->id,
            'remark' => 'Bad student',
            'date' => '2024-12-31'
        ],
        [
            'person_in_charge_id' => $person_in_charge_B->id,
            'student_id' => $student_B->id,
            'hostel_reward_punishment_setting_id' => $hostel_reward_punishment_settings_C->id,
            'remark' => 'Good Student',
            'date' => '2024-01-01'
        ],
        [
            'person_in_charge_id' => $person_in_charge_B->id,
            'student_id' => $student_B->id,
            'hostel_reward_punishment_setting_id' => $hostel_reward_punishment_settings_A->id,
            'remark' => 'Good Student',
            'date' => '2023-10-20'
        ],
    ));

    SnappyPdf::fake();
    $filters = [
        'report_language' => 'en',
        'export_type' => ExportType::PDF->value,
        'student_id' => $student_A->id,
        'date_from' => '2024-01-03',
        'date_to' => '2024-12-30',
    ];

    $filename = 'hostels-report-by-reward-punishment-student';
    $extension = '.pdf';

    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) use ($filename, $extension) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn($filename . $extension);
    });

    $response = $this->getJson(
        route($this->routeNamePrefix . 'by-reward-punishment-student', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])->toMatch("/$filename/")
        ->toEndWith($extension);

    // date from and date to don't have same year
    $filters = [
        'report_language' => 'en',
        'export_type' => ExportType::PDF->value,
        'student_id' => $student_A->id,
        'date_from' => '2024-01-03',
        'date_to' => '2025-12-30',
    ];

    $response = $this->getJson(
        route($this->routeNamePrefix . 'by-reward-punishment-student', $filters)
    )->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'code' => '422',
            'error' => [
                'date_to' => [
                    'Date from and Date to must have the same year'
                ],
            ]
        ]);

    // student_id does not exist
    $filters = [
        'report_language' => 'en',
        'export_type' => ExportType::PDF->value,
        'student_id' => 10000,
        'date_from' => '2024-01-03',
        'date_to' => '2024-12-30',
    ];

    $response = $this->getJson(
        route($this->routeNamePrefix . 'by-reward-punishment-student', $filters)
    )->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'code' => '422',
            'error' => [
                'student_id' => [
                    'The selected student is invalid.'
                ],
            ]
        ]);
});

test('reportByRewardPunishmentRoom return pdf', function () {

    // person in charge
    $person_in_charge_A = Employee::factory()->create();
    $person_in_charge_B = Employee::factory()->create();

    // student
    $student_A = Student::factory()->create();
    $student_B = Student::factory()->create();

    Media::factory()->create([
        'model_type' => Student::class,
        'model_id' => $student_A->id,
        'file_name' => 'first_file.png',
        'collection_name' => 'photo'
    ]);

    // hostel block sample data
    $hostel_block_A = HostelBlock::factory()->create([
        'name->en' => fake()->name,
        'name->zh' => fake('zh_CN')->name,
        'code' => 'BLOCK-A',
        'type' => HostelBlockType::STUDENT
    ]);

    $hostel_block_B = HostelBlock::factory()->create([
        'name->en' => fake()->name,
        'name->zh' => fake('zh_CN')->name,
        'code' => 'BLOCK-B',
        'type' => HostelBlockType::STUDENT
    ]);

    // hostel room sample data
    $hostel_room_A = HostelRoom::factory()->create([
        'hostel_block_id' => $hostel_block_A->id,
        'name' => strtoupper(fake()->bothify('Room ?##')),
        'gender' => HostelRoomGender::MALE->value,
        'capacity' => 100,
        'remarks' => fake()->realText(),
        'is_active' => true,
    ]);

    $hostel_room_B = HostelRoom::factory()->create([
        'hostel_block_id' => $hostel_block_B->id,
        'name' => strtoupper(fake()->bothify('Room ?##')),
        'gender' => HostelRoomGender::MALE->value,
        'capacity' => 100,
        'remarks' => fake()->realText(),
        'is_active' => true,
    ]);

    // hostel room bed sample data
    $hostel_room_bed_A = HostelRoomBed::factory()->create([
        'hostel_room_id' => $hostel_room_A->id,
        'name' => fake()->ean8(),
        'is_active' => true,
        'status' => HostelRoomBedStatus::OCCUPIED->value
    ]);

    $hostel_room_bed_B = HostelRoomBed::factory()->create([
        'hostel_room_id' => $hostel_room_B->id,
        'name' => fake()->ean8(),
        'is_active' => true,
        'status' => HostelRoomBedStatus::OCCUPIED->value
    ]);

    // hostel bedassignemnt sample data
    $hostel_bed_assignment_A = HostelBedAssignment::factory()->create([
        'assignable_id' => $student_A->id,
        'assignable_type' => Student::class,
        'hostel_room_bed_id' => $hostel_room_bed_A->id,
        'previous_hostel_room_bed_id' => null,
        'assigned_by' => User::factory(),
        'start_date' => strtotime("2024-04-30"),
        'end_date' => null,
        'remarks' => fake()->text(100),
    ]);

    $hostel_bed_assignment_B = HostelBedAssignment::factory()->create([
        'assignable_id' => $student_B->id,
        'assignable_type' => Student::class,
        'hostel_room_bed_id' => $hostel_room_bed_B->id,
        'previous_hostel_room_bed_id' => null,
        'assigned_by' => User::factory(),
        'start_date' => strtotime("2024-04-30"),
        'end_date' => null,
        'remarks' => fake()->text(100),
    ]);

    $hostel_bed_assignment_C = HostelBedAssignment::factory()->create([
        'assignable_id' => $student_B->id,
        'assignable_type' => Student::class,
        'hostel_room_bed_id' => $hostel_room_bed_B->id,
        'previous_hostel_room_bed_id' => null,
        'assigned_by' => User::factory(),
        'start_date' => strtotime("2023-04-30"),
        'end_date' => strtotime("2023-10-30"),
        'remarks' => fake()->text(100),
    ]);

    // merit demerit setting data
    $hostel_merit_demerit_setting_A = HostelMeritDemeritSetting::factory()->create([
        'type' => HostelMeritDemeritType::DEMERIT->value,
        'name' => 'Illegal Item -5 marks'
    ]);

    $hostel_merit_demerit_setting_B = HostelMeritDemeritSetting::factory()->create([
        'type' => HostelMeritDemeritType::DEMERIT->value,
        'name' => 'Other -5 marks'
    ]);

    $hostel_merit_demerit_setting_C = HostelMeritDemeritSetting::factory()->create([
        'type' => HostelMeritDemeritType::MERIT->value,
        'name' => 'Charity +2 marks'
    ]);

    // reward punishment setting data
    $hostel_reward_punishment_settings_A = HostelRewardPunishmentSetting::factory()->create([
        'hostel_merit_demerit_setting_id' => $hostel_merit_demerit_setting_A->id,
        'code' => fake()->uuid(),
        'name' => 'Bring Electronic item to school',
        'points' => -5
    ]);

    $hostel_reward_punishment_settings_B = HostelRewardPunishmentSetting::factory()->create([
        'hostel_merit_demerit_setting_id' => $hostel_merit_demerit_setting_B->id,
        'code' => fake()->uuid(),
        'name' => 'Did not clean toilet/room',
        'points' => -5
    ]);

    $hostel_reward_punishment_settings_C = HostelRewardPunishmentSetting::factory()->create([
        'hostel_merit_demerit_setting_id' => $hostel_merit_demerit_setting_C->id,
        'code' => fake()->uuid(),
        'name' => 'Volunteer at old folks home',
        'points' => 2

    ]);

    // hostel reward punishment record data
    $hostel_reward_punishment_records = HostelRewardPunishmentRecord::factory(3)->create(new Sequence(
        [
            'person_in_charge_id' => $person_in_charge_A->id,
            'student_id' => $student_A->id,
            'hostel_reward_punishment_setting_id' => $hostel_reward_punishment_settings_A->id,
            'remark' => 'Bad student',
            'date' => '2024-06-15'
        ],
        [
            'person_in_charge_id' => $person_in_charge_A->id,
            'student_id' => $student_A->id,
            'hostel_reward_punishment_setting_id' => $hostel_reward_punishment_settings_B->id,
            'remark' => 'Bad student',
            'date' => '2024-12-31'
        ],
        [
            'person_in_charge_id' => $person_in_charge_B->id,
            'student_id' => $student_B->id,
            'hostel_reward_punishment_setting_id' => $hostel_reward_punishment_settings_C->id,
            'remark' => 'Good Student',
            'date' => '2024-01-01'
        ],
        [
            'person_in_charge_id' => $person_in_charge_B->id,
            'student_id' => $student_B->id,
            'hostel_reward_punishment_setting_id' => $hostel_reward_punishment_settings_A->id,
            'remark' => 'Good Student',
            'date' => '2023-10-20'
        ],
    ));

    SnappyPdf::fake();
    $filters = [
        'report_language' => 'en',
        'export_type' => ExportType::PDF->value,
        'date_from' => '2024-01-03',
        'date_to' => '2024-12-30',
    ];

    $filename = 'hostels-report-by-reward-punishment-room';
    $extension = '.pdf';

    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) use ($filename, $extension) {
        $mock->shouldReceive('upload')->twice()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->twice()->andReturn($filename . $extension);
    });

    $response = $this->getJson(
        route($this->routeNamePrefix . 'by-reward-punishment-room', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])->toMatch("/$filename/")
        ->toEndWith($extension);

    // validation testing
    $filters = [
        'report_language' => 'en',
        'export_type' => ExportType::PDF->value,
        'room_id' => 9999,
        'date_from' => '2024-01-03',
        'date_to' => '2025-12-30',
    ];

    $response = $this->getJson(
        route($this->routeNamePrefix . 'by-reward-punishment-room', $filters)
    )->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'code' => '422',
            'error' => [
                'date_to' => [
                    'Date from and Date to must have the same year'
                ],
                'room_id' => [
                    'The selected room id is invalid.'
                ]
            ]
        ]);

    // filtering same date dont throw error
    $filters = [
        'report_language' => 'en',
        'export_type' => ExportType::PDF->value,
        'date_from' => '2024-01-03',
        'date_to' => '2024-01-03',
    ];

    $response = $this->getJson(
        route($this->routeNamePrefix . 'by-reward-punishment-room', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])->toEndWith($extension);
});
