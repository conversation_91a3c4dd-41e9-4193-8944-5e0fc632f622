<?php

namespace App\Services;

use App\Enums\ClassType;
use App\Helpers\ErrorCodeHelper;
use App\Models\ClassModel;
use App\Models\ClassSubject;
use App\Models\ClassSubjectStudent;
use App\Models\CurrentStudentClassAndGrade;
use App\Models\HistoricalStudentClassAndGrade;
use App\Models\LatestPrimaryClassBySemesterSettingView;
use App\Models\SemesterClass;
use App\Models\StudentClass;
use App\Models\StudentTimetable;
use App\Repositories\ClassRepository;
use App\Repositories\SemesterClassRepository;
use App\Repositories\StudentClassRepository;
use App\Repositories\StudentRepository;
use Carbon\Carbon;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection as SupportCollection;
use Illuminate\Support\Facades\DB;

class ClassService
{
    private ClassRepository $classRepository;
    private StudentClassRepository $studentClassRepository;
    private StudentRepository $studentRepository;
    private SemesterClassRepository $semesterClassRepository;

    public function __construct(
        ClassRepository $class_repository,
        StudentClassRepository $student_class_repository,
        StudentRepository $student_repository,
        SemesterClassRepository $semester_class_repository,
    ) {
        $this->classRepository = $class_repository;
        $this->studentClassRepository = $student_class_repository;
        $this->studentRepository = $student_repository;
        $this->semesterClassRepository = $semester_class_repository;
    }

    public function getAllPaginatedClasses($filters = []): LengthAwarePaginator
    {
        return $this->classRepository->getAllPaginated($filters);
    }

    public function getAllClasses($filters = []): Collection
    {
        return $this->classRepository->getAll($filters);
    }

    public function createClass($data): ?Model
    {
        return $this->classRepository->create($data);
    }

    public function updateClass(ClassModel $class, $data): ?Model
    {
        return $this->classRepository->update($class, $data);
    }

    public function deleteClass(ClassModel $class): bool
    {
        if (!$class->canBeDeleted()) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::CLASS_ERROR, 6004);
        }

        return $this->classRepository->delete($class);
    }

    public function assignClassToSemester(array $data): void
    {
        $class_ids = Arr::pluck($data['classes'], 'id');

        $class_datas = Arr::keyBy($data['classes'], 'id');

        $existing_semester_class = $this->semesterClassRepository->getAll([
            'semester_setting_id' => $data['semester_setting_id'],
            'class_id' => $class_ids
        ])->keyBy('class_id')->toArray();

        DB::transaction(function () use ($class_datas, $existing_semester_class, $class_ids, $data) {
            foreach ($class_datas as $class_data) {
                if (isset($existing_semester_class[$class_data['id']])) {
                    $this->semesterClassRepository->update($existing_semester_class[$class_data['id']]['id'], [
                        'is_active' => $class_data['is_active'],
                        'homeroom_teacher_id' => $class_data['homeroom_teacher_id'] ?? null,
                    ]);
                } else {
                    $create_payload = [
                        'semester_setting_id' => $data['semester_setting_id'],
                        'class_id' => $class_data['id'],
                        'homeroom_teacher_id' => $class_data['homeroom_teacher_id'] ?? null,
                        'is_active' => $class_data['is_active']
                    ];

                    $this->semesterClassRepository->create($create_payload);
                }
            }
        });
    }

    public function assignClassesToSemesters(array $data): void
    {
        $combinations = collect();

        foreach ($data['semester_setting_ids'] as $semester_setting_id) {
            foreach ($data['class_ids'] as $class_id) {
                $combinations->push([
                    'semester_setting_id' => $semester_setting_id,
                    'class_id' => $class_id,
                ]);
            }
        }

        // get existing semester_classes by using semester_setting_ids, class_ids
        $existing_semester_classes = $this->semesterClassRepository->getAll([
            'class_id' => $data['class_ids'],
            'semester_setting_id' => $data['semester_setting_ids'],
        ])->keyBy(fn($record) => "{$record->class_id}//{$record->semester_setting_id}");

        $prepared = $combinations
            // Filter out combinations that already exist
            ->reject(fn($combination) => $existing_semester_classes->has(
                "{$combination['class_id']}//{$combination['semester_setting_id']}"
            ))
            // prepare data
            ->map(fn($combination) => array_merge($combination, [
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ]));

        $this->semesterClassRepository->insert($prepared->toArray());
    }

    public function assignSemestersToClass(array $data): void
    {
        $semester_setting_datas = Arr::keyBy($data['semester_settings'], 'id');

        $existing_semester_class = $this->semesterClassRepository->getAll([
            'class_id' => $data['class_id'],
        ])->keyBy('semester_setting_id')->toArray();

        DB::transaction(function () use ($semester_setting_datas, $existing_semester_class, $data) {
            foreach ($semester_setting_datas as $semester_setting_data) {
                if (isset($existing_semester_class[$semester_setting_data['id']])) {
                    $this->semesterClassRepository->update($existing_semester_class[$semester_setting_data['id']]['id'], [
                        'is_active' => $semester_setting_data['is_active'],
                        'homeroom_teacher_id' => $semester_setting_data['homeroom_teacher_id'] ?? null,
                    ]);
                } else {
                    $create_payload = [
                        'class_id' => $data['class_id'],
                        'semester_setting_id' => $semester_setting_data['id'],
                        'homeroom_teacher_id' => $semester_setting_data['homeroom_teacher_id'] ?? null,
                        'is_active' => $semester_setting_data['is_active']
                    ];

                    $this->semesterClassRepository->create($create_payload);
                }
            }
        });
    }

    public function assignClassToStudent(array $data): void
    {
        $payload_student_ids = Arr::pluck($data['students'], 'id');

        $class = $this->classRepository->findBySemesterClassId($data['semester_class_id']);

        $existing_classes = $this->getExistingClasses($data, $class);

        $existing_student_ids_in_class = $this->studentClassRepository->getAll([
            'semester_class_id' => $data['semester_class_id'],
            'student_id' => $payload_student_ids,
            'is_latest_class_in_semester' => true,
        ])->pluck('student_id')->toArray();

        $new_student_ids = array_diff($payload_student_ids, $existing_student_ids_in_class);

        $left_class_students = array_diff_key($existing_classes, array_flip($payload_student_ids));

        foreach ($left_class_students as $index => $student_class) {
            // Do not meddle with other class data
            if ($student_class['semester_class_id'] != $data['semester_class_id']) {
                unset($left_class_students[$index]);
            }
        }

        DB::transaction(function () use ($class, $data, $existing_classes, $left_class_students, $new_student_ids) {
            $this->processAssignClassToStudents($data['students'], $class, $data, $existing_classes);
//             If remove from payload, set is_active to false

            if (!empty($left_class_students)) {
                $left_class_student_class_ids = Arr::pluck($left_class_students, 'id');

                $this->studentClassRepository->bulkUpdate($left_class_student_class_ids, [
                    'is_active' => false,
                    'class_leave_date' => now(),
                    'is_latest_class_in_semester' => false
                ]);
            }

            $left_class_student_ids = Arr::pluck($left_class_students, 'student_id');

//            $this->removeStudentsFromPrimaryClassSubjects($payload_student_ids);
            $this->removeStudentsFromPrimaryClassSubjects($left_class_student_ids);

            if (isset($data['class_subject_ids'])) {
                $this->attachStudentsToClassSubjects($new_student_ids, $data);
            }

            LatestPrimaryClassBySemesterSettingView::refreshViewTable();
            StudentTimetable::refreshViewTable();
            HistoricalStudentClassAndGrade::refreshViewTable();
            CurrentStudentClassAndGrade::refreshViewTable();
        });
    }

    public function getStudentsBySemesterClassId($semester_class_id): Collection
    {
        return $this->studentClassRepository->getAll([
            'includes' => ['student'],
            'semester_class_id' => $semester_class_id,
            'for_seats' => true, // all active student with or without seats  +  all inactive students with seats
        ]);
    }

    public function assignSeats(array $seats): void
    {
        $seats = collect($seats);

        $payload = $seats->map(function ($seat) {
            return [
                'id' => $seat['student_class_id'],
                'seat_no' => $seat['seat_no'],
            ];
        })->toArray();

        $this->studentClassRepository->bulkUpdateStudentClassSeats($payload);
    }

    public function autoAssignSeatsBySemester(int $semester_setting_id)
    {
        $this->validateAutoAssignSeats($semester_setting_id);

        $semester_class_ids = SemesterClass::where('semester_setting_id', $semester_setting_id)->pluck('id');

        $student_classes = $this->studentClassRepository->getActiveStudentsBySemesterClassIds($semester_class_ids->toArray());

        $assigned_student_classes = $this->processStudentsForSeatAssignments($student_classes);

        $this->studentClassRepository->bulkUpdateStudentClassSeats($assigned_student_classes->toArray());
    }

    private function processStudentsForSeatAssignments(SupportCollection $student_classes)
    {
        return $student_classes->groupBy('semester_class_id')
            ->map(function ($semester_class) {
                // Sort the group by English name
                $sorted_class = $semester_class->sortBy(function ($student) {
                    $name = json_decode($student->student_name, true);
                    return $name['en'];
                })->values();

                // Assign seat_no sequentially
                $sorted_class->each(function (&$student, $index) {
                    $student->seat_no = $index + 1;
                });

                return $sorted_class;
            })
            ->flatten(1) // ungroup from semester_class_id
            ->values()
            ->map(function ($student_class) {
                // Cast obj_std class to array
                return (array) $student_class;
            });
    }

    private function validateAutoAssignSeats(int $semester_setting_id): void
    {
        /**
         *  check in the semester, if there is already seat assignment to the class
         */
        $is_student_assigned = StudentClass::where('semester_setting_id', $semester_setting_id)->whereNotNull('seat_no')->exists();

        if ($is_student_assigned) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::CLASS_ERROR, 6006);
        }
    }

    private function attachStudentsToClassSubjects(array $payload_student_ids, array $data): void
    {
        $class_subjects = ClassSubject::whereIn('id', $data['class_subject_ids'])->get();

        foreach ($class_subjects as $class_subject) {
            $class_subject->students()->attach($payload_student_ids); // attach students to each class_subject
        }
    }

    private function removeStudentsFromPrimaryClassSubjects(array $payload_student_ids)
    {
        if (count($payload_student_ids) <= 0) {
            return;
        }

        $students = $this->studentRepository->getAll([
            'id' => $payload_student_ids,
            'includes' => [
                'inactivePrimaryClasses.semesterClass.classSubjects',
            ],
        ]);

        $to_be_removed_class_subjects = $students
            ->pluck('inactivePrimaryClasses.*.semesterClass.classSubjects.*.id', 'id')
            ->filter();

        foreach ($to_be_removed_class_subjects as $student_id => $class_subject_ids) {
            // Delete class subjects where student_id and class_subject_id equals
            ClassSubjectStudent::where('student_id', $student_id)
                ->whereIn('class_subject_id', $class_subject_ids)
                ->delete();
        }
    }

    private function getExistingClasses(array $data, ClassModel $class, array $student_ids = []): array
    {
        $query = [
            'semester_setting_id' => $data['semester_setting_id'],
            'class_type' => $class->type,
            'is_latest_class_in_semester' => true,
        ];

        if (!empty($student_ids)) {
            $query['student_id'] = $student_ids;
        }

        // PRIMARY class can have only one active class per semester per student
        if ($class->type !== ClassType::PRIMARY) {
            $query['semester_class_id'] = $data['semester_class_id'];
        }

        return $this->studentClassRepository->getAll($query)->keyBy('student_id')->toArray();
    }

    private function processAssignClassToStudents(array $students, $class, array $data, array $existing_classes): void
    {
        $create_data = [];
        $inactive_ids = [];
        $now = now();

        foreach ($students as $student) {
            $create_payload = $this->getAssignClassToStudentPayload($student, $data, $class);
            $create_payload['created_at'] = $now;
            $create_payload['updated_at'] = $now;

            if ($class->type === ClassType::PRIMARY) {
                if (isset($existing_classes[$student['id']])) {
                    $existing_class = $existing_classes[$student['id']];
                    if ($this->hasClassEnterDateChanged($existing_class, $student)) {
                        $inactive_ids[] = $existing_class['id'];
                        $create_data[] = $create_payload;
                    } else {
                        $this->studentClassRepository->updateById($existing_class['id'], $create_payload);
                    }
                } else {
                    $create_data[] = $create_payload;
                }
            } elseif (in_array($class->type, [ClassType::ENGLISH, ClassType::SOCIETY])) {
                if (isset($existing_classes[$student['id']])) {
                    $this->studentClassRepository->updateById($existing_classes[$student['id']]['id'], $create_payload);
                } else {
                    $create_data[] = $create_payload;
                }
            }
        }

        if ($create_data) {
            $this->studentClassRepository->insert($create_data);
        }

        if ($inactive_ids) {
            $this->studentClassRepository->bulkUpdate($inactive_ids, [
                'is_active' => false,
                'is_latest_class_in_semester' => false
            ]);
        }
    }

    private function getAssignClassToStudentPayload(array $student, array $data, $class): array
    {
        return [
            'semester_setting_id' => $data['semester_setting_id'],
            'semester_class_id' => $data['semester_class_id'],
            'class_type' => $class->type,
            'student_id' => $student['id'],
            'class_enter_date' => $student['class_enter_date'],
            'class_leave_date' => null,
            'is_active' => true, // Assigning is always true $student['is_active'],
            'is_latest_class_in_semester' => true
        ];
    }

    private function hasClassEnterDateChanged(array $existing_class, array $student): bool
    {
        return Carbon::parse($existing_class['class_enter_date'])->diffInDays($student['class_enter_date']) != 0;
    }
}
