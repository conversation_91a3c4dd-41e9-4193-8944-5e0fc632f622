<?php

namespace App\Services\Reports;

use App\Enums\ClassType;
use App\Models\Exam;
use App\Models\Grade;
use App\Models\ResultSourceSubject;
use App\Models\ResultsPostingLineItem;
use App\Models\SemesterClass;
use App\Models\StudentClass;
use App\Models\Subject;
use App\Repositories\SemesterSettingRepository;
use App\Services\BaseReportService;
use App\Services\ReportPrintService;

class AcademyReportService extends BaseReportService
{
    public function __construct(
        protected ReportPrintService $reportPrintService,
        protected SemesterSettingRepository $semesterSettingRepository,

    ) {
    }

    const array EXAM_RESULT_BANDS = [
        '0-9.9' => [
            'from' => 0,
            'to' => 9.99
        ],
        '10-19.99' => [
            'from' => 10,
            'to' => 19.99
        ],
        '20-29.99' => [
            'from' => 20,
            'to' => 29.99
        ],
        '30-39.99' => [
            'from' => 30,
            'to' => 39.99
        ],
        '40-49.99' => [
            'from' => 40,
            'to' => 49.99
        ],
        '50-59.99' => [
            'from' => 50,
            'to' => 59.99
        ],
        '60-69.99' => [
            'from' => 60,
            'to' => 69.99
        ],
        '70-79.99' => [
            'from' => 70,
            'to' => 79.99,
        ],
        '80-89.99' => [
            'from' => 80,
            'to' => 89.99,
        ],
        '90-100' => [
            'from' => 90,
            'to' => 100,
        ]
    ];

    public function getNetAveragePassingRateReportData(array $filters): array
    {

        $includes = [
            'semesterClass:id,class_id',
            'semesterClass.classModel:id,code,name',
            'gradingScheme:id,pass_mark'
        ];

        // Only Non-Exempted/Non-Absent students will have Result Posting Line Item, hence no need to filter
        $data = ResultsPostingLineItem::select('id', 'grade_id', 'header_id', 'semester_class_id', 'grading_scheme_id', 'total')
            ->with($includes)
            ->where(['report_card_output_component_code' => 'SYS_NET_AVG'])
            ->whereIn('grade_id', $filters['grade_ids'])
            ->whereHas('header', function ($query) use ($filters) {
                $query->whereIn('header_id', $filters['result_posting_header_ids']);
            })
            ->get()
            ->groupBy('semesterClass.classModel.name');

        $data->transform(function ($class) {
            $total_students = $class->count();
            $pass_student = 0;

            foreach ($class as $student) {
                $pass_student += $student->total >= (float) $student->gradingScheme->pass_mark ? 1 : 0;
            }

            $pass_rate = bcmul(bcdiv($pass_student, $total_students, 4), 100, 2);
            return $pass_rate;
        });

        $data = $data->sortKeys();

        $grades = Grade::whereIn('id', $filters['grade_ids'])
            ->get()
            ->transform(function ($grade) { return mb_convert_case($grade->name, MB_CASE_TITLE, "UTF-8"); })
            ->implode(', ');

        $semester_setting_name = $this->semesterSettingRepository->getAll(['id' => $filters['semester_setting_id']])->first()->name;
        $title = __('exam.net_average_passing_rate_report_title', ['semester_setting_name' => $semester_setting_name, 'grade_name' => $grades]);

        $data = [
            'title' => $title,
            'report_data' => $data->toArray()
        ];

        return $data;
    }

    public function getSubjectPassingRateReportData(array $filters)
    {
        $semester_year = $this->semesterSettingRepository->getSemesterYearBySemesterSetting($filters['semester_setting_id']);

        $student_class_map = $this->getStudentClassMapBySemesterAndGrade($filters);
        $students = array_keys($student_class_map);

        $includes = [
            'components:id,result_source_subject_id,actual_score,weightage_percent',
            'resultSource:id,code,student_grading_framework_id',
            'resultSource.studentGradingFramework:id,student_id,academic_year'
        ];

        $result_data = ResultSourceSubject::select('id', 'result_source_id', 'subject_id', 'actual_score', 'is_exempted', 'pass_mark')
            ->with($includes)
            ->where('subject_id', $filters['subject_id'])
            ->whereHas('resultSource', function ($query) use ($filters, $students, $semester_year) {
                $query->whereRelation('exams', 'exam_id', $filters['exam_id'])
                    ->whereHas('studentGradingFramework', function ($query) use ($students, $semester_year) {
                        $query->where('academic_year', $semester_year)
                            ->whereIn('student_id', $students);
                    });
            })
            ->whereHas('components', function ($query) {
                $query->whereNotNull('actual_score');
            })
            ->where('is_exempted', false)
            ->get();


        $result_data->transform(function ($result_source_subject) {
            $marks = 0;
            if (isset($result_source_subject->actual_score) && !is_null($result_source_subject->actual_score)) {
                $marks = $result_source_subject->actual_score;
            } else {
                foreach ($result_source_subject->components as $component) {
                    $marks += bcmul($component->actual_score, bcdiv($component->weightage_percent, 100, 2), 2);
                }
            }

            $student_result_data = [
                'student_id' => $result_source_subject->resultSource->studentGradingFramework->student_id,
                'passed_exam' => $marks >= $result_source_subject->pass_mark
            ];

            return $student_result_data;

        });

        $report_data = [];

        foreach ($result_data as $result) {
            $class = $student_class_map[$result['student_id']];

            if (!isset($report_data[$class])) {
                $report_data[$class]['total_students'] = 0;
                $report_data[$class]['pass_students'] = 0;
            }

            $report_data[$class]['total_students'] += 1;
            $report_data[$class]['pass_students'] += $result['passed_exam'] ? 1 : 0;
        }

        $report_data = collect($report_data)->transform(function ($data) {
            return bcmul(bcdiv($data['pass_students'], $data['total_students'], 4), 100, 2);
        })->sortKeys();

        $grade_name = mb_convert_case(Grade::find($filters['grade_id'])->name, MB_CASE_TITLE, 'UTF-8');
        $exam_name = Exam::find($filters['exam_id'])->name;
        $subject_name = Subject::find($filters['subject_id'])->name;
        $title = __('exam.subject_passing_rate_report_title', ['exam_name' => $exam_name, 'grade_name' => $grade_name, 'subject_name' => $subject_name]);

        $data = [
            'title' => $title,
            'report_data' => $report_data->toArray()
        ];

        return $data;
    }

    public function getSubjectAverageMarkReportData(array $filters)
    {
        $semester_year = $this->semesterSettingRepository->getSemesterYearBySemesterSetting($filters['semester_setting_id']);

        $student_class_map = $this->getStudentClassMapBySemesterAndGrade($filters);
        $students = array_keys($student_class_map);

        $includes = [
            'components:id,result_source_subject_id,actual_score,weightage_percent',
            'resultSource:id,code,student_grading_framework_id',
            'resultSource.studentGradingFramework:id,student_id,academic_year'
        ];

        $result_data = ResultSourceSubject::select('id', 'result_source_id', 'subject_id', 'actual_score', 'is_exempted', 'pass_mark')
            ->with($includes)
            ->where('subject_id', $filters['subject_id'])
            ->whereHas('resultSource', function ($query) use ($filters, $students, $semester_year) {
                $query->whereRelation('exams', 'exam_id', $filters['exam_id'])
                    ->whereHas('studentGradingFramework', function ($query) use ($students, $semester_year) {
                        $query->where('academic_year', $semester_year)
                            ->whereIn('student_id', $students);
                    });
            })
            ->whereHas('components', function ($query) {
                $query->whereNotNull('actual_score');
            })
            ->where('is_exempted', false)
            ->get();

        $result_data->transform(function ($result_source_subject) {
            $marks = 0;
            if (isset($result_source_subject->actual_score) && !is_null($result_source_subject->actual_score)) {
                $marks = $result_source_subject->actual_score;
            } else {
                foreach ($result_source_subject->components as $component) {
                    $marks += bcmul($component->actual_score, bcdiv($component->weightage_percent, 100, 2), 2);
                }
            }

            $student_result_data = [
                'student_id' => $result_source_subject->resultSource->studentGradingFramework->student_id,
                'marks' => $marks,
            ];

            return $student_result_data;
        });

        $report_data = [];
        foreach ($result_data as $result) {
            $class = $student_class_map[$result['student_id']];

            if (!isset($report_data[$class])) {
                $report_data[$class]['total_marks'] = 0;
                $report_data[$class]['total_students'] = 0;
            }

            $report_data[$class]['total_marks'] += $result['marks'];
            $report_data[$class]['total_students'] += 1;
        }

        $report_data = collect($report_data)->transform(function ($data) {
            return bcdiv($data['total_marks'], $data['total_students'], 2);
        })->sortKeys();

        $grade_name = mb_convert_case(Grade::find($filters['grade_id'])->name, MB_CASE_TITLE, 'UTF-8');
        $exam_name = Exam::find($filters['exam_id'])->name;
        $subject_name = Subject::find($filters['subject_id'])->name;
        $title = __('exam.subject_average_mark_report_title', ['exam_name' => $exam_name, 'grade_name' => $grade_name, 'subject_name' => $subject_name]);

        $data = [
            'title' => $title,
            'report_data' => $report_data->toArray()
        ];

        return $data;
    }

    public function getSubjectScoreAnalysisReportData(array $filters)
    {
        $semester_year = $this->semesterSettingRepository->getSemesterYearBySemesterSetting($filters['semester_setting_id']);

        $students = StudentClass::select('student_id')
            ->whereRelation('semesterClass', 'id', $filters['semester_class_id'])
            ->get()
            ->transform(function ($student) { return $student->student_id; })
            ->toArray();

        $includes = [
            'components:id,result_source_subject_id,actual_score,weightage_percent',
            'subject:id,name',
            'resultSource:id,code,student_grading_framework_id',
            'resultSource.studentGradingFramework:id,student_id,academic_year'
        ];

        $result_data = ResultSourceSubject::select('id', 'result_source_id', 'subject_id', 'is_exempted', 'actual_score')
            ->with($includes)
            ->whereHas('resultSource', function ($query) use ($filters, $students, $semester_year) {
                $query->whereRelation('exams', 'exam_id', $filters['exam_id'])
                    ->whereHas('studentGradingFramework', function ($query) use ($students, $semester_year) {
                        $query->where('academic_year', $semester_year)
                            ->whereIn('student_id', $students);
                    });
            })
            ->whereHas('components', function ($query) {
                $query->whereNotNull('actual_score');
            })
            ->where('is_exempted', false)
            ->get()
            ->groupBy('subject.name');

        $result_data->transform(function ($result_source_subject_by_subject) {

            $subject_marks = collect();
            foreach ($result_source_subject_by_subject as $result_source_subject) {
                $marks = 0;
                if (isset($result_source_subject->actual_score) && !is_null($result_source_subject->actual_score)) {
                    $marks = $result_source_subject->actual_score;
                } else {
                    foreach ($result_source_subject->components as $component) {
                        $marks += bcmul($component->actual_score, bcdiv($component->weightage_percent, 100, 2), 2);
                    }
                }
                $subject_marks->push(['marks' => $marks]);
            }

            $mark_counts = [];
            foreach ($this::EXAM_RESULT_BANDS as $exam_key => $exam_band) {
                $mark_count = $subject_marks->whereBetween('marks', [$exam_band['from'], $exam_band['to']])->count();
                $mark_counts[$exam_key] = $mark_count;
            }

            return $mark_counts;
        });

        $exam_name = Exam::find($filters['exam_id'])->name;
        $class_name = SemesterClass::find($filters['semester_class_id'])->load('classModel')->classModel->name;
        $title = __('exam.subject_score_analysis_report_title', ['exam_name' => $exam_name, 'class_name' => $class_name]);

        $data = [
            'title' => $title,
            'report_data' => $result_data->toArray()
        ];
        return $data;
    }

    public function getStudentClassMapBySemesterAndGrade(array $filters)
    {
        $student_class_includes = [
            'semesterClass:id,class_id',
            'semesterClass.classModel:id,code,grade_id,name',
        ];

        $student_class_map = StudentClass::select('id', 'semester_class_id', 'class_type', 'semester_setting_id',
            'is_latest_class_in_semester', 'student_id')
            ->with($student_class_includes)
            ->whereRelation('semesterClass.classModel', 'grade_id', $filters['grade_id'])
            ->where([
                'semester_setting_id' => $filters['semester_setting_id'],
                'is_latest_class_in_semester' => true,
                'class_type' => ClassType::PRIMARY->value
            ])
            ->get()
            ->groupBy('student_id')
            ->transform(function ($class) {
                return $class[0]->semesterClass->classModel->name;
            });

        return $student_class_map->toArray();
    }
}
