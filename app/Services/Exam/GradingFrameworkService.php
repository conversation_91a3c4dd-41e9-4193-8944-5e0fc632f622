<?php

namespace App\Services\Exam;

use App\Jobs\ApplyGradingFrameworkJob;
use App\Jobs\UpdateGradingFrameworkJob;
use App\Models\GradingFramework;
use App\Models\Student;
use App\Repositories\GradingFrameworkRepository;
use App\Repositories\StudentGradingFrameworkRepository;
use App\Repositories\StudentRepository;
use App\Services\Exam\Output\GeneralReportCardOutputService;
use App\Services\Exam\Output\PinHwaReportCardOutputV1Service;
use Carbon\Carbon;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class GradingFrameworkService
{
    private array $gradingFrameworkData = [];
    private bool $replaceExistingStudentGradingFramework = false;

    public function __construct(
        protected GradingFrameworkRepository $gradingFrameworkRepository,
        protected StudentGradingFrameworkService $studentGradingFrameworkService,
        protected StudentGradingFrameworkRepository $studentGradingFrameworkRepository,
        protected StudentRepository $studentRepository
    ) {
    }

    public function getAllGradingFrameworks($filters = []): Collection
    {
        return $this->gradingFrameworkRepository->getAll($filters);
    }

    public function getAllPaginatedGradingFrameworks($filters = []): LengthAwarePaginator
    {
        return $this->gradingFrameworkRepository->getAllPaginated($filters);
    }

    public function createGradingFramework(): ?Model
    {
        return $this->gradingFrameworkRepository->create($this->getGradingFrameworkData());
    }

    public function updateGradingFramework(GradingFramework $grading_framework): ?Model
    {
        return DB::transaction(function () use ($grading_framework) {
            $grading_framework_data = $this->getGradingFrameworkData();
            $updated_grading_framework = $this->gradingFrameworkRepository->update($grading_framework, $grading_framework_data);

            if ($grading_framework_data['is_active'] && $this->replaceExistingStudentGradingFramework) {
                $grading_framework->load('activeStudentGradingFrameworks.student.primaryClass');
                $student_grading_frameworks = $grading_framework->activeStudentGradingFrameworks;

                foreach ($student_grading_frameworks as $student_grading_framework) {
                    if (app()->environment('testing')) {
                        UpdateGradingFrameworkJob::dispatch($student_grading_framework->id, $grading_framework);
                    } else {
                        UpdateGradingFrameworkJob::dispatch($student_grading_framework->id, $grading_framework)->onConnection('grading-framework')->onQueue('grading-framework');
                    }
                }
            }
            return $updated_grading_framework;
        });
    }

    public function setGradingFrameworkData($data): self
    {
        if ($data['is_active'] == true) {
            $this->studentGradingFrameworkService->validateConfiguration($data['configuration'], true);
        }

        $this->gradingFrameworkData = [
            'name' => $data['name'],
            'code' => $data['code'],
            'is_active' => $data['is_active'],
            'configuration' => $data['configuration'] ?? null,
        ];
        return $this;
    }

    public function getGradingFrameworkData(): array
    {
        return $this->gradingFrameworkData;
    }

    public function validateData($config): bool
    {
        return $this->studentGradingFrameworkService->validateConfiguration($config);

    }

    public function bulkApplyStudentGradingFrameworkByClass($input)
    {
        $input['semester_class_ids'] = array_map('intval', $input['semester_class_ids']);
        $students = $this->studentRepository->getAll([
            'latest_semester_class_id' => $input['semester_class_ids'],
            'includes' => ['primaryClass'],
        ]);

        $grading_framework = GradingFramework::find($input['grading_framework_id']);

        foreach ($students as $student){
            if (app()->environment('testing')) {
                ApplyGradingFrameworkJob::dispatch($student, $grading_framework, $input);
            } else {
                ApplyGradingFrameworkJob::dispatch($student, $grading_framework, $input)->onConnection('grading-framework')->onQueue('grading-framework');
            }
        }
    }

    public function applyStudentGradingFramework($input)
    {
        $students = Student::with('primaryClass')
            ->whereIn('id', $input['student_ids'])
            ->get();
        
        foreach ($students as $student){
            $grading_framework = GradingFramework::find($input['grading_framework_id']);
            if (app()->environment('testing')) {
                ApplyGradingFrameworkJob::dispatch($student, $grading_framework, $input);
            } else {
                ApplyGradingFrameworkJob::dispatch($student, $grading_framework, $input)->onConnection('grading-framework')->onQueue('grading-framework');
            }
        }
    }

    public function replaceExistingStudentGradingFramework(bool $replace_existing): self
    {
        $this->replaceExistingStudentGradingFramework = $replace_existing;
        return $this;
    }

    public function getAllFixedFormulas(): Collection
    {
        $data = collect();

        // Able to change this in a factory when more school are supported
        $service = resolve(PinHwaReportCardOutputV1Service::class);

        foreach ($service::FORMULA as $key => $value) {
            $formula_data = [
                'label' => $key,
                'value' => $value['value'],
                'args' => $value['args'] ?? []
            ];
            $data->push($formula_data);
        }

        return $data;
    }

    public function getAllResultSourceFormulas(GradingFramework $grading_framework, array $filters = []): Collection
    {
        $data = collect();
        $config = $grading_framework->configuration;

        // This happens when the grading framework result source is not configured and user directly configure output formulas
        // Thus we need to set the result source to empty array
        $result_sources = $config['result_sources'] ?? [];

        foreach ($result_sources as $result_source) {
            $code = $result_source['code'] ?? null;

            if (empty($code) || (isset($filters['result_source_code']) && $code != $filters['result_source_code'])) {
                continue;
            }

            $subjects = $result_source['subjects'] ?? [];
            foreach ($subjects as $subject) {

                $subject_code = $subject['code'];

                if (isset($filters['subject_code']) && $subject_code != $filters['subject_code']) {
                    continue;
                }

                foreach (array_keys(GeneralReportCardOutputService::TARGET_KEYWORDS) as $keyword) {
                    if (empty($subject_code)) {
                        continue;
                    }

                    $formula = [
                        'label' => $code . '.' . $subject_code . '.' . $keyword,
                        'value' => "VAR(\"RESULTSOURCE[" . $code . "].SUBJECT[" . $subject_code . "].$keyword\")",
                    ];
                    $data->push($formula);
                }
            };
        }

        return $data;
    }

    public function getAllOutputFormulas(GradingFramework $grading_framework, $filters = []): Collection
    {

        $data = collect();
        $config = $grading_framework->configuration;
        $report_outputs = $config['output'] ?? [];

        foreach ($report_outputs as $output) {

            $code = $output['code'] ?? null;

            if (empty($code) || (isset($filters['output_code']) && $code != $filters['output_code'])) {
                continue;
            }

            $components = $output['components'];
            foreach ($components as $component) {

                $component_code = $component['code'] ?? null;

                if (isset($filters['component_code']) && $component_code != $filters['component_code']) {
                    continue;
                }

                foreach (array_keys(GeneralReportCardOutputService::OUTPUT_TARGET_KEYWORDS) as $keyword) {
                    if (empty($component_code)) {
                        continue;
                    }

                    $formula = [
                        'label' => $code . '.' . $component_code . '.' . $keyword,
                        'value' => "VAR(\"OUTPUT[" . $code . "].COMPONENT[" . $component_code . "].$keyword\")",
                    ];
                    $data->push($formula);
                }
            };
        }

        return $data;
    }
}
