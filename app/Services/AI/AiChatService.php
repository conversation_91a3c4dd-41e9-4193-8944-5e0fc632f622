<?php

namespace App\Services\AI;

use App\Interfaces\Userable;
use App\Models\Attendance;
use App\Models\Book;
use App\Models\User;
use App\Models\WalletTransaction;
use App\Repositories\AiChatHistoryRepository;
use App\Repositories\BookRepository;
use Illuminate\Support\Facades\DB;
use Prism\Prism\Enums\Provider;
use Prism\Prism\Facades\Tool;
use Prism\Prism\Prism;
use Prism\Prism\Schema\StringSchema;
use Prism\Prism\ValueObjects\Messages\AssistantMessage;
use Prism\Prism\ValueObjects\Messages\UserMessage;

class AiChatService
{
    const Provider AI_PROVIDER = Provider::Mistral;
    const string AI_MODEL = 'mistral-small-latest';
//    const Provider AI_PROVIDER = Provider::Ollama;
//    const string AI_MODEL = 'mistral-small';

//    const Provider AI_PROVIDER = Provider::Gemini;
//    const string AI_MODEL = 'gemini-2.5-flash';

    private User $user;
    private Userable $userable;
    private string $sessionId;
    private string $message;

    public function __construct(
        protected AiChatHistoryRepository $aiChatHistoryRepository,
        protected BookRepository $bookRepository
    ) {

    }

    public function setRequestingUser(User $user): self
    {
        $this->user = $user;

        return $this;
    }

    public function getRequestingUser(): User
    {
        return $this->user;
    }

    public function setUserable(Userable $userable): self
    {
        $this->userable = $userable;

        return $this;
    }

    public function getUserable(): Userable
    {
        return $this->userable;
    }

    public function setSessionId($session_id): self
    {
        $this->sessionId = $session_id;

        return $this;
    }

    public function getSessionId(): string
    {
        return $this->sessionId;
    }

    public function setMessage($message): self
    {
        $this->message = $message;

        return $this;
    }

    public function getMessage(): string
    {
        return $this->message;
    }

    public function sendMessage()
    {
        $chat = $this->aiChatHistoryRepository->create([
            'user_id' => $this->getRequestingUser()->id,
            'userable_id' => $this->getUserable()->id,
            'userable_type' => get_class($this->getUserable()),
            'session_id' => $this->getSessionId(),
            'message' => $this->getMessage(),
        ]);

        $previous_chats = $this->aiChatHistoryRepository->getChatBySessionId($this->getSessionId());

        $chat_history = $this->mapChatHistoryToMessages($previous_chats);

        try {
            $response = Prism::text()
                ->using(self::AI_PROVIDER, self::AI_MODEL)
                ->withSystemPrompt('
                The current date is ' . now()->toIso8601String() . '.
                You are a very very friendly chat bot.
                You are a school management system for Pin Hwa High School.
                Your name is "Skribby", a friendly assistant for Pin Hwa High School.
                You are here to help students, teachers, and parents with their queries.
                Your reply should be polite and professional.
                You can help with:
                - Finding library books by title or author
                ')
                ->withMaxSteps(2)
                ->withMessages($chat_history)
                ->withTools([
                    $this->toolFindLibraryBookByTitleOrAuthor(),
                    $this->toolWalletSpendingStatistics($this->getUserable()),
                    $this->toolStudentAttendance($this->getUserable()),
                ])
                ->asText();

            $chat->response = $response->text;
            $chat->save();
        } catch (\Exception $e) {
            dd($e);
        }

        dd($response->text, $response);
    }

    public function mapChatHistoryToMessages($chats)
    {
        return $chats->map(function ($chat) {
            $messages = [];
            $messages[] = new UserMessage($chat->message);
            if ($chat->response) {
                $messages[] = new AssistantMessage($chat->response);
            }

            return $messages;
        })->flatten()->toArray();
    }

    public function toolFindLibraryBookByTitleOrAuthor()
    {
        return Tool::as('find_library_book_by_title_or_author')
            ->for('Find a library book by title or author')
            ->withObjectParameter(
                'book',
                'The book to find',
                [
                    new StringSchema('title', 'The title of the book'),
                    new StringSchema('author', 'The author of the book'),
                ],
                requiredFields: []
            )->using(function ($book) {
                $books = Book::query()
                    ->when($book['title'] ?? null, fn($query, $title) => $query->where('title', 'ilike', '%' . $title . '%'))
                    ->when($book['author'] ?? null,
                        fn($query, $author) => $query->whereHas('authors',
                            fn($query) => $query->where('name', 'ilike', '%' . $author . '%')))
                    ->limit(10)
                    ->get();

                $format_book_prompt = $books->map(function ($book) {
                    return "Title: {$book->title}, Author: {$book->authors->pluck('name')->join(', ')}, Status: {$book->status->value}\n";
                });

                return "I found the following books: .\n" . $format_book_prompt . "\n Put the result in this format
                {Book Title}
                {Author: {List of author}}
                {Status: {Status}}
                ";
            });
    }

    public function toolWalletSpendingStatistics($userable)
    {
        $user_id = $userable->id;

        return Tool::as('wallet_spending_statistics')
            ->for('This tool is used to get wallet spending statistics,
               it takes a date range query (in natural language) as input and returns the total spending amount and the number of transactions.
            ')
            ->withObjectParameter(
                'wallet_query',
                'The date query to get wallet spending statistics',
                [
                    new StringSchema(
                        'date_from',
                        'Specify a time period for spending statistics using natural language.
                        For example, "last month", "this week", "last 30 days", etc.
                        Translate the natural language into a earliest date.
                        The date range MUST be in the format of "YYYY-MM-DD".
                        ',
                        false // Make this required in the schema itself
                    ),
                    new StringSchema(
                        'date_to',
                        'Specify a time period for spending statistics using natural language.
                        For example, "last month", "this week", "last 30 days", etc.
                        Translate the natural language into a latest date.
                        The date range MUST be in the format of "YYYY-MM-DD".
                        ',
                        false // Make this required in the schema itself
                    ),
                    new StringSchema(
                        'gender',
                        'The gender of the user to filter the spending statistics.
                        User will normally use words like "son", "daughter" or "child" to refer to their children.
                        This is optional, if not provided, it will return the spending statistics for current.',
                    )
                ],
                requiredFields: ['date_from', 'date_to']
            )->using(function ($wallet_query) use ($user_id) {
                $date_from = $wallet_query['date_from'];
                $date_to = $wallet_query['date_to'];

                $gender = $wallet_query['gender'] ?? null;

                $wallet_transactions = WalletTransaction::whereRelation('wallet', 'user_id', $user_id)
                    ->whereBetween('created_at', [$date_from, $date_to])
                    ->where('status', 'SUCCESS')
                    ->get()
                    ->groupBy('type');

                $spendings = $wallet_transactions['TRANSACTION'] ?? collect();
                $spendings = abs($spendings->sum('amount_after_tax'));

                return 'Spendings between ' . $date_from . ' and ' . $date_to . ' is RM' . $spendings;
            });
    }

    public function toolStudentAttendance($userable): \Prism\Prism\Tool
    {
        return Tool::as('student_attendance')
            ->for('This tool is used to get student attendance statistics,
               it takes a date range query (in natural language) as input and returns the total number of classes and the number of classes attended.
            ')
            ->withObjectParameter(
                'student_attendance_query',
                'The date query to get student attendance statistics.',
                [
                    new StringSchema(
                        'date_from',
                        'Specify a time period for attendance statistic using natural language.
                        For example, "last month", "this week", "last 30 days", etc.
                        Translate the natural language into a earliest date.
                        The date range MUST be in the format of "YYYY-MM-DD".
                        ',
                        false // Make this required in the schema itself
                    ),
                    new StringSchema(
                        'date_to',
                        'Specify a time period for attendance statistics using natural language.
                        For example, "last month", "this week", "last 30 days", etc.
                        Translate the natural language into a latest date.
                        The date range MUST be in the format of "YYYY-MM-DD".
                        ',
                        false // Make this required in the schema itself
                    ),
                ],
                requiredFields: ['date_from', 'date_to']
            )->using(function ($student_attendance_query) use ($userable) {

                $date_from = $student_attendance_query['date_from'];
                $date_to = $student_attendance_query['date_to'];

                $student_attendance = Attendance::select([DB::raw('count(*) as count'), 'status'])
                    ->where('attendance_recordable_type', $userable->getMorphClass())
                    ->where('attendance_recordable_id', $userable->id)
                    ->whereBetween('date', [$date_from, $date_to])
                    ->groupBy('status')
                    ->get();

                $present_count = $student_attendance->firstWhere('status', 'PRESENT')->count ?? 0;
                $absent_count = $student_attendance->firstWhere('status', 'ABSENT')->count ?? 0;

                return 'Student attendance statistics from ' . $date_from . ' to ' . $date_to . ': ' .
                    $present_count . ' days present and ' . $absent_count . ' days absent.';
            });
    }

    public function getDateRangeFromQuery($range): array
    {
        $range = explode(' - ', $range);

        return [
            'date_from' => $range[0],
            'date_to' => $range[1],
        ];
    }
}
