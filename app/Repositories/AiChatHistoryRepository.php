<?php


namespace App\Repositories;

use App\Models\AiChatHistory;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class AiChatHistoryRepository extends BaseRepository
{

    public function getModelClass(): string
    {
        return AiChatHistory::class;
    }

    public function getQuery($filters = []): Builder
    {
        return parent::getQuery($filters)
            ->when(isset($filters['session_id']), function (Builder $query) use ($filters) {
                $query->where('session_id', $filters['session_id']);
            });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        // TODO: Implement getAllPaginated() method.
    }

    public function getChatBySessionId(string $session_id): Collection
    {
        return $this->getAll([
            'session_id' => $session_id,
            'order_by' => ['created_at', 'asc']
        ]);
    }
}
