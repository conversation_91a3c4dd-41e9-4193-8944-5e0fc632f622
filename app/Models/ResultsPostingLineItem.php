<?php

namespace App\Models;

use App\Traits\Models\HasTranslations;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ResultsPostingLineItem extends Model
{
    use HasFactory;

    protected $table = 'results_posting_line_items';

    protected $guarded = ['id'];

    protected $casts = [
        'total_grade' => 'array',
    ];

    public function header() {
        return $this->belongsTo(ResultsPostingHeader::class);
    }

    public function student() {
        return $this->belongsTo(Student::class);
    }

    public function reportCardOutputComponent() {
        return $this->belongsTo(ReportCardOutputComponent::class, 'report_card_output_component_id');
    }

    public function subject() {
        return $this->belongsTo(Subject::class, 'subject_id');
    }

    public function semesterClass(){
        return $this->belongsTo(SemesterClass::class, 'semester_class_id');
    }

    public function gradingScheme(){
        return $this->belongsTo(GradingScheme::class, 'grading_scheme_id');
    }

    public function getValue($output_type) {

        if ( in_array( $output_type, ['total', 'label']) ) {
            return $this->{$output_type};
        }
        else if ( $output_type == 'total_grade' ) {
            return $this->{$output_type}['display_as_name'];
        }
        else{
            return 'ERR';
        }

    }
}
