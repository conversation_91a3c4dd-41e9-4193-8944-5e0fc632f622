<?php

namespace App\Helpers;

use App\Enums\AuditAction;
use App\Enums\Module;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use Psr\Log\LogLevel;

class LogHelper
{
    const LOG_NAME_GENERAL = 'GENERAL';
    const LOG_NAME_USER_ACTIVITY = 'USER_ACTIVITY';
    const LOG_NAME_INBOUND_API = 'INBOUND_API';
    const LOG_NAME_OUTBOUND_API = 'OUTBOUND_API';
    const LOG_NAME_UNPAID_ITEM_ASSIGNMENTS = 'UNPAID_ITEM_ASSIGNMENTS';
    const LOG_NAME_ACCOUNTING = 'ACCOUNTING';
    const LOG_NAME_PUSH_NOTIFICATION = 'PUSH_NOTIFICATION';
    const LOG_NAME_PAYMENT_GATEWAY = 'PAYMENT_GATEWAY';
    const LOG_NAME_ATTENDANCE = 'ATTENDANCE';
    const LOG_NAME_ENROLLMENT_SESSION = 'ENROLLMENT_SESSION';

    public static function activity(Module $module, AuditAction $action, $message)
    {

        $actor = \Auth::user();

        if ($actor === null) {
            return;
        }

        $data = [
            'actor' => $actor->id,      // user id
            'actor_email' => $actor->email,
            'actor_phone' => $actor->phone_number,
            'module' => $module->value,
            'action' => $action->value,
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ];

        LogHelper::write($message, $data, LogLevel::DEBUG, LogHelper::LOG_NAME_USER_ACTIVITY);

    }

    public static function write($message, $data = null, $level = LogLevel::INFO, $log_name = self::LOG_NAME_GENERAL)
    {

        $driver = strtolower(config('school.log_driver'));

        // need to change to 2019-02-01T03:45:27.123+00:00 (yyyy-MM-dd'T'HH:mm:ss.SSSZZ) format
        $now = Carbon::now()->format('Y-m-d\TH:i:s.vP');

        if ($driver === 'logstash') {

            $log_name = strtolower($log_name);
            $log_name = str_replace("_", '-', $log_name);

            $payload = [
                'client' => strtolower(config('school.identifier')),        // must be lower case as it's part of index name
                'environment' => strtolower(app()->environment()),        // must be lower case as it's part of index name
                'message' => $message,
                'data' => $data,
                'log_name' => $log_name,
                'log_level' => $level,
                'origin_timestamp' => $now,
                'driver' => $driver
            ];

            // add to redis
            Redis::connection('logging')->rpush('pending-logs', json_encode($payload));

        } else {
            if ($driver === 'file') {

                switch ($level) {
                    case LogLevel::WARNING:
                        Log::warning($message);
                        if ($data !== null) {
                            Log::warning($data);
                        }
                        break;
                    case LogLevel::ERROR:
                        Log::error($message);
                        if ($data !== null) {
                            Log::error($data);
                        }
                        break;
                    case LogLevel::DEBUG:
                        Log::debug($message);
                        if ($data !== null) {
                            Log::debug($data);
                        }
                        break;
                    default:
                        Log::info($message);
                        if ($data !== null) {
                            Log::info($data);
                        }
                        break;
                }

            } else {
                throw new \Exception('Unsupported log driver.');
            }
        }
    }

    public static function clearPendingLogs()
    {
        Redis::connection('logging')->del('pending-logs');
    }

    public static function getPendingLogs()
    {
        return Redis::connection('logging')->lrange('pending-logs', 0, -1);
    }
}
