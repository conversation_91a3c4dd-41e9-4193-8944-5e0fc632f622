<?php

namespace App\Http\Controllers\Api;

use App\Factories\UserableFactory;
use App\Http\Controllers\Controller;
use App\Services\AI\AiChatService;
use Illuminate\Http\Request;
use Prism\Prism\Enums\Provider;

class AIController extends Controller
{
    //

    const AI_PROVIDER = Provider::Mistral;
    const AI_MODEL = 'mistral-small-latest';
//    const AI_PROVIDER = Provider::DeepSeek;
//    const AI_MODEL = 'deepseek-chat';
//    const AI_PROVIDER = Provider::Gemini;
//    const AI_MODEL = 'gemini-2.5-flash';

    public function __construct(protected AiChatService $aiChatService)
    {

    }

    public function create()
    {
//        return AiChatHistory::create([
//            'session_id' => uniqid()
//        ]);

        return uniqid('ai-chat-', true);
    }

    public function chat(Request $request)
    {
        $data = $request->validate([
            'userable_type' => 'required',
            'userable_id' => 'required',
            'session_id' => 'required',
            'message' => 'required'
        ]);

        $userable = (new UserableFactory($data['userable_type']))->find($data['userable_id']);

        $this->aiChatService
            ->setRequestingUser(auth()->user())
            ->setUserable($userable)
            ->setSessionId($data['session_id'])
            ->setMessage($data['message'])
            ->sendMessage();
    }
}
