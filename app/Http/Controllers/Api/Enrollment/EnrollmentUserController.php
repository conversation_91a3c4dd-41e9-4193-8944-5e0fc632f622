<?php

namespace App\Http\Controllers\Api\Enrollment;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Enrollment\EnrollmentUserIndexRequest;
use App\Http\Requests\Api\Enrollment\EnrollmentUserUpdateRequest;
use App\Http\Resources\ApiResponse;
use App\Http\Resources\EnrollmentUserResource;
use App\Models\EnrollmentUser;
use App\Services\EnrollmentUserService;
use App\Traits\HandlesPagination;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class EnrollmentUserController extends Controller
{
    use HandlesPagination;

    protected EnrollmentUserService $enrollmentUserService;

    public function __construct(EnrollmentUserService $enrollment_user_service)
    {
        $this->enrollmentUserService = $enrollment_user_service;
    }

    public function index(EnrollmentUserIndexRequest $request): JsonResponse
    {
        /** @var User $user */
        $user = Auth::user();

        $input = $request->validated();

        $api_response = (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200);

        $data = $this->fetchData($input,
            $this->enrollmentUserService,
            'getAllEnrollmentUsers',
            'getAllPaginatedEnrollmentUsers');

        $this->determinePagination($api_response, $input, $data);

        return $api_response->setData(EnrollmentUserResource::collection($data))->getResponse();
    }

    public function show(EnrollmentUser $enrollment_user): JsonResponse
    {
        $enrollment_user->load(['enrollments']);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new EnrollmentUserResource($enrollment_user))
            ->getResponse();
    }

//    public function create(EnrollmentUserCreateRequest $request): JsonResponse
//    {
//        $input = $request->validated();
//        $enrollment_user = $this->enrollmentUserService->createEnrollmentUser($input);
//
//        return (new ApiResponse())
//            ->setMessage(__('api.common.success'))
//            ->setCode(200)
//            ->setData(new EnrollmentUserResource($enrollment_user))
//            ->getResponse();
//    }


    public function update(EnrollmentUser $enrollment_user, EnrollmentUserUpdateRequest $request): JsonResponse
    {
        $input = $request->validated();

        $enrollment_user = $this->enrollmentUserService->updateEnrollmentUser($enrollment_user, $input);

        $enrollment_user->load(['enrollments']);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new EnrollmentUserResource($enrollment_user))
            ->getResponse();
    }

    public function destroy(EnrollmentUser $enrollment_user): JsonResponse
    {
        $this->enrollmentUserService->deleteEnrollmentUser($enrollment_user);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->getResponse();
    }
}
