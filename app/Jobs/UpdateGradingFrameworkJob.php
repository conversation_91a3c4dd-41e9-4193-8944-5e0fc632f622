<?php

namespace App\Jobs;

use App\Models\GradingFramework;
use App\Models\StudentGradingFramework;
use App\Services\Exam\StudentGradingFrameworkService;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class UpdateGradingFrameworkJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 1;
    public int $studentGradingFrameworkID;
    public GradingFramework $gradingFramework;

    /**
     * Create a new job instance.
     */
    public function __construct(int $student_grading_framework_id, GradingFramework $grading_framework)
    {
        //$this->student = $student;
        $this->gradingFramework = $grading_framework;
        $this->studentGradingFrameworkID = $student_grading_framework_id;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {

            $service = app()->make(StudentGradingFrameworkService::class);
            $student_grading_framework = StudentGradingFramework::with('student')
                ->select(['id', 'student_id', 'grading_framework_id', 'effective_from', 'effective_to', 'is_active', 'academic_year'])
                ->find($this->studentGradingFrameworkID);

            $effective_from = Carbon::parse($student_grading_framework->effective_from);
            $effective_to = Carbon::parse($student_grading_framework->effective_to);

            $service->setStudent($student_grading_framework->student)
                ->setGradingFramework($this->gradingFramework)
                ->setStudentGradingFramework($student_grading_framework)
                ->updateGradingFramework($effective_from, $effective_to);
        } catch (\Exception $e) {
            \Log::error($e->getMessage());
            \Log::error($e->getTraceAsString());
        }
    }
}
